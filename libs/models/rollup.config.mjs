import { DEFAULT_EXTENSIONS } from '@babel/core';
import { babel } from '@rollup/plugin-babel';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';

const extensions = [...DEFAULT_EXTENSIONS, '.ts'];

export default {
  input: 'src/index.ts',
  output: [
    {
      format: 'esm',
      dir: 'dist/',
      preserveModules: true,
      exports: 'named',
    }
  ],
  plugins: [
    resolve({
      browser: true,
      extensions,
    }),
    commonjs(),
    babel({
      extensions,
      babelHelpers: 'runtime',
    }),
  ],
  external: [/node_modules/]
}
