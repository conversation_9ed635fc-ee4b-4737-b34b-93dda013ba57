import { differenceWith, isEqual } from 'lodash';

import { History } from '../history';
import {
  DBLoadingJobJson,
  GSQLLoadingJobJson,
  LoadingJobData,
  LoadingJobLogic,
  LoadToEdgeData,
  LoadToVectorData,
  LoadToVertexData,
  OneColumnMapping,
  SourceType,
} from '../loading-job';
import { Graph, GSQLEdgeJson, GSQLGraphJson, GSQLVertexJson, GSQLUdtJson } from '../topology';
import { LoadingJobUtilityService } from './loading-job-utility.service';

export class LoadingJobMigrationLogicService {
  constructor(private loadingJobUtility: LoadingJobUtilityService) {}

  /**
   * Migrate the loading jobs according to schema change history.
   *
   * ASSUMPTIONS:
   * 1. In each schema change step, only one vertex or edge can be modified.
   * (the most important rule cannot be broken)
   * 2. In each schema change step, one or more vertex or edge types can be created.
   * (not supported in Schema Designer yet)
   * 3. In each schema change step, one or more vertex or edge types can be removed.
   *
   * @param {History<Graph>} schemaChangeHistory
   * @param {GSQLLoadingJobJson[]} gsqlLoadingJobsJson
   * @param {DBLoadingJobJson[]} dbLoadingJobsJson
   * @param {GSQLUdtJson[]} udtList
   * @returns {LoadingJobData[]} the miration result.
   * @memberof LoadingJobMigrationLogicService
   */
  migrateLoadingJobs(
    schemaChangeHistory: History<Graph>,
    gsqlLoadingJobsJson: GSQLLoadingJobJson[],
    dbLoadingJobsJson: DBLoadingJobJson[],
    udtList: GSQLUdtJson[]
  ): LoadingJobData[] {
    if (schemaChangeHistory.getHistoryLength() < 1) {
      return [];
    }

    // Use the original schema, GSQL loading job and DB loading job information to construct
    // the in-memory loading jobs.
    const originalLoadingJobs = this.loadingJobUtility.loadLoadingJobsFromGSQLAndDB(
      schemaChangeHistory.getRecord(0),
      gsqlLoadingJobsJson,
      dbLoadingJobsJson,
      udtList
    );

    // Clone the loading jobs.
    const migratedLoadingJobs: LoadingJobData[] = originalLoadingJobs.map((loadingJob) =>
      LoadingJobLogic.clone(loadingJob)
    );

    // Replay the schema change history and make the migration step by step.
    for (let i = 1; i < schemaChangeHistory.getHistoryLength(); i++) {
      const prevSchema = schemaChangeHistory.getRecord(i - 1).dumpToGSQLJson();
      const nextSchema = schemaChangeHistory.getRecord(i).dumpToGSQLJson();

      // Skip if the one-step change doesn't affect existing vertex types and edge types.
      if (this.schemaChangeNotTouchExistingVertexAndEdge(prevSchema, nextSchema)) {
        continue;
      }

      // Handle delete vertex or edge type case.
      if (this.afterSchemaChangeAllRemainingVertexAndEdgeStaySame(prevSchema, nextSchema)) {
        // Get removed vertex type names and edge type names.
        const [removedVertexTypeNames, removedEdgeTypeNames] = this.getRemovedVertexAndEdgeTypes(
          prevSchema,
          nextSchema
        );
        // Remove the loading statements loading to these vertex types and edge types.
        this.removeLoadingStatementsToRemovedVertexEdgeTypes(
          migratedLoadingJobs,
          removedVertexTypeNames,
          removedEdgeTypeNames
        );
        continue;
      }

      // Handle modified one vertex type or edge type case.
      const [modifiedVertexTypeInPrevSchema, modifiedVertexTypeInNextSchema] = this.getModifiedVertexType(
        prevSchema,
        nextSchema
      );
      if (modifiedVertexTypeInPrevSchema) {
        // Handle modified one vertex type case.
        this.migrateLoadingJobsForOneVertexModification(
          migratedLoadingJobs,
          modifiedVertexTypeInPrevSchema,
          modifiedVertexTypeInNextSchema
        );
      } else {
        // Handle modified one edge type case.
        const [modifiedEdgeTypeInPrevSchema, modifiedEdgeTypeInNextSchema] = this.getModifiedEdgeType(
          prevSchema,
          nextSchema
        );
        this.migrateLoadingJobsForOneEdgeModification(
          migratedLoadingJobs,
          modifiedEdgeTypeInPrevSchema,
          modifiedEdgeTypeInNextSchema
        );
      }
    }

    return migratedLoadingJobs;
  }

  /**
   * Check if the one-step schema change doesn't affect existing vertex types and edge types.
   * This happens when user is adding new vertex or edge types, or changing graph schema style.
   *
   * @private
   * @param {GSQLGraphJson} prevSchema
   * @param {GSQLGraphJson} nextSchema
   * @returns {boolean}
   * @memberof LoadingJobMigrationLogicService
   */
  private schemaChangeNotTouchExistingVertexAndEdge(prevSchema: GSQLGraphJson, nextSchema: GSQLGraphJson): boolean {
    // Check if all vertex types in prevSchema keep same in nextSchema.
    if (differenceWith(prevSchema.VertexTypes, nextSchema.VertexTypes, isEqual).length > 0) {
      return false;
    }
    // Check if all edge types in prevSchema keep same in nextSchema.
    if (differenceWith(prevSchema.EdgeTypes, nextSchema.EdgeTypes, isEqual).length > 0) {
      return false;
    }
    return true;
  }

  /**
   * Check if after one-step schema change all vertex and edge types remaining in schema keeps same
   * as before the schema change (except changing schema style).
   * This happens when user is deleting some vertex or edge types.
   *
   * @private
   * @param {GSQLGraphJson} prevSchema
   * @param {GSQLGraphJson} nextSchema
   * @returns {boolean}
   * @memberof LoadingJobMigrationLogicService
   */
  private afterSchemaChangeAllRemainingVertexAndEdgeStaySame(
    prevSchema: GSQLGraphJson,
    nextSchema: GSQLGraphJson
  ): boolean {
    // Check if all vertex types and edge types in nextSchema remained same from prevSchema.
    if (differenceWith(nextSchema.VertexTypes, prevSchema.VertexTypes, isEqual).length > 0) {
      return false;
    }
    if (differenceWith(nextSchema.EdgeTypes, prevSchema.EdgeTypes, isEqual).length > 0) {
      return false;
    }
    return true;
  }

  /**
   * Get all removed vertex type names and edge type names.
   *
   * @private
   * @param {GSQLGraphJson} prevSchema
   * @param {GSQLGraphJson} nextSchema
   * @returns {[string[], string[]]}
   * @memberof LoadingJobMigrationLogicService
   */
  private getRemovedVertexAndEdgeTypes(prevSchema: GSQLGraphJson, nextSchema: GSQLGraphJson): [string[], string[]] {
    const removedVertexTypeNames = differenceWith<GSQLVertexJson, GSQLVertexJson>(
      prevSchema.VertexTypes,
      nextSchema.VertexTypes,
      isEqual
    ).map((vertexType) => vertexType.Name);
    const removedEdgeTypeNames = differenceWith<GSQLEdgeJson, GSQLEdgeJson>(
      prevSchema.EdgeTypes,
      nextSchema.EdgeTypes,
      isEqual
    ).map((edgeType) => edgeType.Name);

    return [removedVertexTypeNames, removedEdgeTypeNames];
  }

  /**
   * Remove the loading statements loading to removed vertex and edge types.
   *
   * @private
   * @param {LoadingJobData[]} loadingJobs
   * @param {string[]} removedVertexTypeNames
   * @param {string[]} removedEdgeTypeNames
   * @memberof LoadingJobMigrationLogicService
   */
  private removeLoadingStatementsToRemovedVertexEdgeTypes(
    loadingJobs: LoadingJobData[],
    removedVertexTypeNames: string[],
    removedEdgeTypeNames: string[]
  ) {
    loadingJobs.forEach((loadingJob) => {
      // Filter out the loading statements that are not going to be removed.
      loadingJob.loadingStatements = loadingJob.loadingStatements.filter(
        (loadingStatement) =>
          ('vertexName' in loadingStatement &&
            !removedVertexTypeNames.includes((<LoadToVertexData>loadingStatement).vertexName)) ||
          ('edgeName' in loadingStatement &&
            !removedEdgeTypeNames.includes((<LoadToEdgeData>loadingStatement).edgeName) &&
            !removedVertexTypeNames.includes((<LoadToEdgeData>loadingStatement).fromVertexType) &&
            !removedVertexTypeNames.includes((<LoadToEdgeData>loadingStatement).toVertexType))
      );
    });
  }

  /**
   * Get the modified vertex type in prevSchema and nextSchema.
   * If no vertex is changed, return [undefined, undefined].
   *
   * @private
   * @param {GSQLGraphJson} prevSchema
   * @param {GSQLGraphJson} nextSchema
   * @returns {[GSQLVertexJson, GSQLVertexJson]}
   * @memberof LoadingJobMigrationLogicService
   */
  private getModifiedVertexType(
    prevSchema: GSQLGraphJson,
    nextSchema: GSQLGraphJson
  ): [GSQLVertexJson, GSQLVertexJson] {
    return [
      differenceWith<GSQLVertexJson, GSQLVertexJson>(prevSchema.VertexTypes, nextSchema.VertexTypes, isEqual)[0],
      differenceWith<GSQLVertexJson, GSQLVertexJson>(nextSchema.VertexTypes, prevSchema.VertexTypes, isEqual)[0],
    ];
  }

  /**
   * Get the modified edge type in prevSchema and nextSchema.
   * This must be checked after we make sure that there is no vertex modified, because modifying
   * one vertex name will trigger all edges linking to that vertex to be changed.
   *
   * @private
   * @param {GSQLGraphJson} prevSchema
   * @param {GSQLGraphJson} nextSchema
   * @returns {[GSQLEdgeJson, GSQLEdgeJson]}
   * @memberof LoadingJobMigrationLogicService
   */
  private getModifiedEdgeType(prevSchema: GSQLGraphJson, nextSchema: GSQLGraphJson): [GSQLEdgeJson, GSQLEdgeJson] {
    return [
      differenceWith<GSQLEdgeJson, GSQLEdgeJson>(prevSchema.EdgeTypes, nextSchema.EdgeTypes, isEqual)[0],
      differenceWith<GSQLEdgeJson, GSQLEdgeJson>(nextSchema.EdgeTypes, prevSchema.EdgeTypes, isEqual)[0],
    ];
  }

  /**
   * Migrate the loading jobs according to modification of one vertex type.
   *
   * @private
   * @param {LoadingJobData[]} loadingJobs
   * @param {GSQLVertexJson} vertexTypeInPrevSchema
   * @param {GSQLVertexJson} vertexTypeInNextSchema
   * @memberof LoadingJobMigrationLogicService
   */
  private migrateLoadingJobsForOneVertexModification(
    loadingJobs: LoadingJobData[],
    vertexTypeInPrevSchema: GSQLVertexJson,
    vertexTypeInNextSchema: GSQLVertexJson
  ) {
    loadingJobs.forEach((loadingJob) => {
      // If the embedding attributes are changed, we need to remove the statements
      loadingJob.loadingStatements = loadingJob.loadingStatements.filter(
        (loadingStatement) =>
          !(
            'vertexName' in loadingStatement &&
            (<LoadToVectorData>loadingStatement).vertexName ===
              vertexTypeInPrevSchema.Name &&
            'vectorName' in loadingStatement &&
            !vertexTypeInNextSchema.EmbeddingAttributes.some(
              (embeddingAttribute) =>
                embeddingAttribute.Name ===
                (<LoadToVectorData>loadingStatement).vectorName
            )
          )
      );
      // Migrate the loading statements mapping to the changed vertex.
      const affectedLoadToVertexStatements = loadingJob.loadingStatements.filter(
        (loadingStatement) =>
          'vertexName' in loadingStatement &&
          (<LoadToVertexData>loadingStatement).vertexName === vertexTypeInPrevSchema.Name
      );

      affectedLoadToVertexStatements.forEach((loadingStatement) => {
        // 1. Update vertex name.
        (<LoadToVertexData>loadingStatement).vertexName = vertexTypeInNextSchema.Name;

        // Create the new mapping array.
        const newColumnMappings: OneColumnMapping[] = [];
        // 2. Update PRIMARY_ID to default mapping if type is changed.
        if (!isEqual(vertexTypeInPrevSchema.PrimaryId.AttributeType, vertexTypeInNextSchema.PrimaryId.AttributeType)) {
          newColumnMappings.push({
            // @ts-ignore
            sourceType: SourceType.Default,
          });
        } else {
          newColumnMappings.push(loadingStatement.mappings[0]);
        }
        // 3. Migrate the column mapping for attributes.
        if ('vectorName' in loadingStatement) {
          newColumnMappings.push(loadingStatement.mappings[1]);
        } else {
          this.migrateAttributeColumnMappings(
            vertexTypeInPrevSchema,
            vertexTypeInNextSchema,
            loadingStatement.mappings.slice(1),
            newColumnMappings
          );
        }

        // Replace the old column mapping array.
        loadingStatement.mappings = newColumnMappings;
      });

      // Migrate loading statements mapping to edges touched the changed vertex.
      loadingJob.loadingStatements
        .filter((loadingStatement) => 'edgeName' in loadingStatement)
        .forEach((loadingStatement) => {
          this.migrateLoadToEdgeDataSourceVertex(
            vertexTypeInPrevSchema,
            vertexTypeInNextSchema,
            <LoadToEdgeData>loadingStatement
          );

          this.migrateLoadToEdgeDataTargetVertex(
            vertexTypeInPrevSchema,
            vertexTypeInNextSchema,
            <LoadToEdgeData>loadingStatement
          );
        });
    });
  }

  /**
   * Migrate the loading jobs according to modification of one edge type.
   *
   * @private
   * @param {LoadingJobData[]} loadingJobs
   * @param {GSQLEdgeJson} edgeTypeInPrevSchema
   * @param {GSQLEdgeJson} edgeTypeInNextSchema
   * @memberof LoadingJobMigrationLogicService
   */
  private migrateLoadingJobsForOneEdgeModification(
    loadingJobs: LoadingJobData[],
    edgeTypeInPrevSchema: GSQLEdgeJson,
    edgeTypeInNextSchema: GSQLEdgeJson
  ) {
    loadingJobs.forEach((loadingJob) => {
      const affectedLoadToEdgeStatements = loadingJob.loadingStatements.filter(
        (loadingStatement) =>
          'edgeName' in loadingStatement && (<LoadToEdgeData>loadingStatement).edgeName === edgeTypeInPrevSchema.Name
      );

      affectedLoadToEdgeStatements.forEach((loadingStatement) => {
        // 1. Update edge name.
        (<LoadToEdgeData>loadingStatement).edgeName = edgeTypeInNextSchema.Name;

        // 2. If the edge's source or target vertex type are changed, and the loading statement
        // is not pointing to the new source and target vertex type, then change the mapping
        // to be default, and change loadToEdgeData source vertex type and target vertex type.
        if (
          edgeTypeInNextSchema.FromVertexTypeName !== '*' &&
          edgeTypeInNextSchema.FromVertexTypeName !== (<LoadToEdgeData>loadingStatement).fromVertexType
        ) {
          (<LoadToEdgeData>loadingStatement).fromVertexType = edgeTypeInNextSchema.FromVertexTypeName;
          loadingStatement.mappings[0] = {
            // @ts-ignore
            sourceType: SourceType.Default,
          };
        }
        if (
          edgeTypeInNextSchema.ToVertexTypeName !== '*' &&
          edgeTypeInNextSchema.ToVertexTypeName !== (<LoadToEdgeData>loadingStatement).toVertexType
        ) {
          (<LoadToEdgeData>loadingStatement).toVertexType = edgeTypeInNextSchema.ToVertexTypeName;
          loadingStatement.mappings[1] = {
            // @ts-ignore
            sourceType: SourceType.Default,
          };
        }

        // Create the new mapping array.
        const newColumnMappings: OneColumnMapping[] = [];
        newColumnMappings.push(loadingStatement.mappings[0]);
        newColumnMappings.push(loadingStatement.mappings[1]);
        // 3. Migrate the column mapping for attributes.
        this.migrateAttributeColumnMappings(
          edgeTypeInPrevSchema,
          edgeTypeInNextSchema,
          loadingStatement.mappings.slice(2),
          newColumnMappings
        );

        // Replace the old column mapping array.
        loadingStatement.mappings = newColumnMappings;
      });
    });
  }

  /**
   * Migrate the column mappings for the attributes.
   * For each attribute in next schema, if there is an exact match attribute in previous
   * schema, use that mapping, otherwise use a default mapping.
   *
   * @private
   * @param {(GSQLVertexJson | GSQLEdgeJson)} vertexOrEdgeTypeInPrevSchema
   * @param {(GSQLVertexJson | GSQLEdgeJson)} vertexOrEdgeTypeInNextSchema
   * @param {OneColumnMapping[]} oldColumnMappings
   * @param {OneColumnMapping[]} newColumnMappings
   * @memberof LoadingJobMigrationLogicService
   */
  private migrateAttributeColumnMappings(
    vertexOrEdgeTypeInPrevSchema: GSQLVertexJson | GSQLEdgeJson,
    vertexOrEdgeTypeInNextSchema: GSQLVertexJson | GSQLEdgeJson,
    oldColumnMappings: OneColumnMapping[],
    newColumnMappings: OneColumnMapping[]
  ) {
    vertexOrEdgeTypeInNextSchema.Attributes.forEach((nextAttribute) => {
      let matched = false;
      vertexOrEdgeTypeInPrevSchema.Attributes.forEach((prevAttribute, i) => {
        if (isEqual(nextAttribute, prevAttribute)) {
          matched = true;
          newColumnMappings.push(oldColumnMappings[i]);
          return;
        }
      });
      if (!matched) {
        newColumnMappings.push({
          // @ts-ignore
          sourceType: SourceType.Default,
        });
      }
    });
  }

  /**
   * Migrate source vertex of LoadToEdgeData according to source vertex change.
   *
   * @private
   * @param {GSQLVertexJson} vertexTypeInPrevSchema
   * @param {GSQLVertexJson} vertexTypeInNextSchema
   * @param {LoadToEdgeData} loadToEdgeData
   * @memberof LoadingJobMigrationLogicService
   */
  private migrateLoadToEdgeDataSourceVertex(
    vertexTypeInPrevSchema: GSQLVertexJson,
    vertexTypeInNextSchema: GSQLVertexJson,
    loadToEdgeData: LoadToEdgeData
  ) {
    if (loadToEdgeData.fromVertexType === vertexTypeInPrevSchema.Name) {
      // Change source vertex type name.
      loadToEdgeData.fromVertexType = vertexTypeInNextSchema.Name;
      // If PRIMARY ID type doesn't match, change source vertex type mapping to default.
      if (!isEqual(vertexTypeInPrevSchema.PrimaryId.AttributeType, vertexTypeInNextSchema.PrimaryId.AttributeType)) {
        loadToEdgeData.mappings[0] = {
          // @ts-ignore
          sourceType: SourceType.Default,
        };
      }
    }
  }

  /**
   * Migrate target vertex of LoadToEdgeData according to target vertex change.
   *
   * @private
   * @param {GSQLVertexJson} vertexTypeInPrevSchema
   * @param {GSQLVertexJson} vertexTypeInNextSchema
   * @param {LoadToEdgeData} loadToEdgeData
   * @memberof LoadingJobMigrationLogicService
   */
  private migrateLoadToEdgeDataTargetVertex(
    vertexTypeInPrevSchema: GSQLVertexJson,
    vertexTypeInNextSchema: GSQLVertexJson,
    loadToEdgeData: LoadToEdgeData
  ) {
    if (loadToEdgeData.toVertexType === vertexTypeInPrevSchema.Name) {
      // Change target vertex type name.
      loadToEdgeData.toVertexType = vertexTypeInNextSchema.Name;
      // If PRIMARY ID type doesn't match, change source vertex type mapping to default.
      if (!isEqual(vertexTypeInPrevSchema.PrimaryId.AttributeType, vertexTypeInNextSchema.PrimaryId.AttributeType)) {
        loadToEdgeData.mappings[1] = {
          // @ts-ignore
          sourceType: SourceType.Default,
        };
      }
    }
  }
}
