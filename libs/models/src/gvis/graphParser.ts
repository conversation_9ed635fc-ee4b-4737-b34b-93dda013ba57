import { ExternalLink, getLinkID } from "./graphchart.data.link";
import { ExternalNode, getNodeID } from "./graphchart.data.node";

export interface ExternalGraph {
  nodes: ExternalNode[];
  links: ExternalLink[];
}

const gquery = {
  isNode: (obj) => {
    let result = true;

    if (typeof obj !== "object" || obj === null) {
      return false;
    } else {
      result = result && typeof obj["v_id"] === "string";
      result = result && typeof obj["v_type"] === "string";
      // result = result && typeof obj['v_set'] === 'string';
      result = result && (typeof obj["v"] === "object" || typeof obj["attributes"] === "object");
    }

    return result;
  },
  isLink: (obj) => {
    let result = true;

    if (typeof obj !== "object" || obj === null) {
      return false;
    } else {
      result = result && typeof obj["e_type"] === "string";
      result = result && typeof obj["from_id"] === "string";
      result = result && typeof obj["from_type"] === "string";
      result = result && typeof obj["to_id"] === "string";
      result = result && typeof obj["to_type"] === "string";
      result = result && typeof obj["directed"] === "boolean";
      result = result && typeof obj["attributes"] === "object";
    }

    return result;
  },
  parseNode: (obj) => {
    let newNode: ExternalNode = {
      id: obj.v_id,
      type: obj.v_type,
      attrs: {},
      embeddingAttrs: [],
      styles: {},
      others: {
        "vertex set": obj.v_set,
      },
      labels: {},
    };

    if (obj.v) {
      Object.keys(obj.v).forEach((attr: string) => {
        newNode.attrs[attr] = obj.v[attr];
      });
    }

    if (obj.attributes) {
      Object.keys(obj.attributes)
        // Remove the @links attribute from kstep_expansion
        .filter((attr: string) => attr !== '@links')
        .forEach((attr: string) => {
          newNode.attrs[attr] = obj.attributes[attr];
        });
    }

    if (obj.Embeddings) {
      newNode.embeddingAttrs = [obj.Embeddings];
    }

    obj.styles = obj.styles || {};
    obj.others = obj.others || {};
    obj.labels = obj.labels || {};

    Object.keys(obj.styles).forEach((key: string) => {
      newNode.styles[key] = obj.styles[key];
    });

    Object.keys(obj.others).forEach((key: string) => {
      newNode.others[key] = obj.others[key];
    });

    Object.keys(obj.labels).forEach((key: string) => {
      newNode.labels[key] = obj.labels[key];
    });

    return newNode;
  },
  parseLink: (obj) => {
    let newLink: ExternalLink = {
      source: {
        id: obj.from_id,
        type: obj.from_type,
      },
      target: {
        id: obj.to_id,
        type: obj.to_type,
      },
      directed: obj.directed,
      type: obj.e_type,
      discriminator: "",
      attrs: {},
      styles: {},
      others: {},
      labels: {},
    };

    Object.keys(obj.attributes).forEach((attr: string) => {
      newLink.attrs[attr] = obj.attributes[attr];
    });

    obj.styles = obj.styles || {};
    obj.others = obj.others || {};
    obj.labels = obj.labels || {};

    newLink.discriminator = obj.discriminator || "";

    Object.keys(obj.styles).forEach((key: string) => {
      newLink.styles[key] = obj.styles[key];
    });

    Object.keys(obj.others).forEach((key: string) => {
      newLink.others[key] = obj.others[key];
    });

    Object.keys(obj.labels).forEach((key: string) => {
      newLink.labels[key] = obj.labels[key];
    });

    return newLink;
  },
};

export function parseGraph(
  externalData: any,
  discriminatorMap?: { [key: string]: string[] }
): ExternalGraph {
  let targetGraph: ExternalGraph = {
    nodes: [],
    links: [],
  };

  try {
    recursivelyGetExternalElement(
      externalData,
      targetGraph,
      new Set<string>(),
      new Set<string>(),
      gquery,
      discriminatorMap,
    );
  } catch (err) {
    console.warn(err);
    targetGraph = {
      nodes: [],
      links: [],
    };
  }

  const patchedGraph = addMissingNode(targetGraph);
  return patchedGraph;
}

function recursivelyGetExternalElement(
  obj: any,
  targetGraph: ExternalGraph,
  visitedNodes: Set<string>,
  visitedLinks: Set<string>,
  format: {
    isNode: { (obj: any): boolean };
    isLink: { (obj: any): boolean };
    parseNode: { (obj: any): ExternalNode };
    parseLink: { (obj: any): ExternalLink };
  },
  discriminatorMap?: { [key: string]: string[] },
) {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  }

  let tempObj = obj.constructor ? obj.constructor() : {};
  for (let key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      tempObj[key] = recursivelyGetExternalElement(
        obj[key],
        targetGraph,
        visitedNodes,
        visitedLinks,
        format,
        discriminatorMap,
      );
    } else {
      throw new Error("External Data format can not be parased.");
    }
  }

  if (format.isNode(tempObj)) {
    const parsedNode = format.parseNode(tempObj);
    const id = `${parsedNode.type}#${parsedNode.id}`;
    if (!visitedNodes.has(id)) {
      visitedNodes.add(id);
      targetGraph.nodes.push(parsedNode);
    }
  }

  if (format.isLink(tempObj)) {
    const parsedLink = format.parseLink(tempObj);
    if (discriminatorMap?.[parsedLink.type]) {
      parsedLink.discriminator = discriminatorMap[parsedLink.type]
        ?.map((d) => parsedLink.attrs?.[d])
        .join(':');
    }
    let id = getLinkID(parsedLink);
    if (!visitedLinks.has(id)) {
      visitedLinks.add(id);
      targetGraph.links.push(parsedLink);
    }
  }

  return tempObj;
}

// edges in graph may have source/target node not in graph
// we try to generate the missing nodes
export function addMissingNode(graph: ExternalGraph): ExternalGraph {
  const { nodes, links } = graph;

  const newNodes = [...nodes];

  const vertIdSet = new Set<string>();

  for (let node of nodes) {
    const id = getNodeID(node);
    vertIdSet.add(id);
  }

  for (let link of links) {
    const source = `${link.source.type}#${link.source.id}`;
    const target = `${link.target.type}#${link.target.id}`;

    if (!vertIdSet.has(source)) {
      vertIdSet.add(source);
      newNodes.push({
        id: link.source.id,
        type: link.source.type,
        attrs: {},
      });
    }

    if (!vertIdSet.has(target)) {
      vertIdSet.add(target);
      newNodes.push({
        id: link.target.id,
        type: link.target.type,
        attrs: {},
      });
    }
  }

  return {
    nodes: newNodes,
    links,
  };
}
