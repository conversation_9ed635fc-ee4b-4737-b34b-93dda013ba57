import { GraphStudioError } from '../error';
import { FormatValidator, ValidateResult } from '../utils';

import { Attribute, GSQLAttributeJson } from './attribute.model';
import { EmbeddingAttribute, GSQLEmbeddingAttributeJson } from './embedding-attribute.model';
import { NodeVis } from './graph-vis.model';
import { VertexStyle } from './vertex-style.model';

/**
 * GSQL Vertex json representation interface following the format:
 *   {
 *     "Name": string,
 *     "PrimaryId": GSQLAttributeJson,
 *     "Attributes": GSQLAttributeJson[],
 *     "Config": { ... }
 *   }
 *
 * @export
 * @interface GSQLVertexJson
 */
export interface GSQLVertexJson {
  Name: string;
  PrimaryId: GSQLAttributeJson;
  Attributes: GSQLAttributeJson[];
  Config: any;
  EmbeddingAttributes?: GSQLEmbeddingAttributeJson[];
  IsLocal?: boolean;
  Usage?: string[];
  CompositeKeys?: string[];
}

/**
 * Vertex describes a vertex type in graph schema, including the vertex type's name,
 * primary_id attributes, and so on.
 *
 * @export
 * @class Vertex
 */
export class Vertex {
  name: string;
  primaryId: Attribute;
  attributes: Attribute[];
  embeddingAttributes: EmbeddingAttribute[];
  config: any;
  style: VertexStyle;
  isLocal: boolean;
  usage: string[];
  compositeKeys: string[];

  /**
   * Creates an instance of Vertex.
   *
   * @memberof Vertex
   */
  constructor() {
    this.name = '';
    this.primaryId = new Attribute();
    this.attributes = [];
    this.embeddingAttributes = [];
    this.config = {
      STATS: 'OUTDEGREE_BY_EDGETYPE'
    };
    this.style = undefined;
    this.isLocal = true;
    this.usage = undefined;
    this.compositeKeys = undefined;
  }

  /**
   * Clone one vertex.
   *
   * @returns {Vertex}
   * @memberof Vertex
   */
  clone(): Vertex {
    const vertex = new Vertex();
    vertex.name = this.name;
    vertex.primaryId = this.primaryId.clone();
    vertex.config = JSON.parse(JSON.stringify(this.config));
    if (this.style !== undefined) {
      vertex.style = this.style.clone();
    }
    this.attributes.forEach((attribute) => {
      vertex.attributes.push(attribute.clone());
    });
    this.embeddingAttributes.forEach((embeddingAttribute) => {
      vertex.embeddingAttributes.push(embeddingAttribute.clone());
    });
    vertex.isLocal = this.isLocal;
    vertex.usage = this.usage;
    vertex.compositeKeys = this.compositeKeys;
    return vertex;
  }

  /**
   * Load Vertex from GSQL Vertex json representation.
   *
   * @param {GSQLVertexJson} vertexJson GSQL Vertex json representation
   * @returns {Vertex}
   * @memberof Vertex
   */
  loadFromGSQLJson(vertexJson: GSQLVertexJson): Vertex {
    // Name
    this.name = vertexJson.Name;

    // PrimaryId
    this.primaryId.loadFromGSQLJson(vertexJson.PrimaryId);

    // Attributes
    this.attributes = (vertexJson.Attributes).map((attr) => {
      const attribute = new Attribute();
      attribute.loadFromGSQLJson(attr);
      return attribute;
    });
    if (this.primaryId.isPrimaryKey) {
      this.attributes = this.attributes.filter((attr) => attr.name !== this.primaryId.name);
    }

    // EmbeddingAttributes
    if (vertexJson.EmbeddingAttributes) {
      this.embeddingAttributes = vertexJson.EmbeddingAttributes.map((attr) => {
        const embeddingAttribute = new EmbeddingAttribute();
        embeddingAttribute.loadFromGSQLJson(attr);
        return embeddingAttribute;
      });
    }

    // Config
    this.config = vertexJson.Config;

    // Is local
    this.isLocal = !!vertexJson.IsLocal;

    // Graph list that global vertex type is used.
    if (vertexJson.Usage) {
      this.usage = vertexJson.Usage;
    }

    // Composite keys
    if (vertexJson.CompositeKeys) {
      this.compositeKeys = vertexJson.CompositeKeys;
    }

    // Do semantic check
    const semanticResult = this.semanticCheck();
    if (!semanticResult.success) {
      throw new GraphStudioError(semanticResult.message);
    }

    return this;
  }

  /**
   * Dump Vertex into GSQL Vertex json representation.
   *
   * @returns {GSQLVertexJson}
   * @memberof Vertex
   */
  dumpToGSQLJson(): GSQLVertexJson {
    const vertexJson: GSQLVertexJson = {
      Name: this.name,
      PrimaryId: this.primaryId.dumpToGSQLJson(),
      Attributes: this.attributes.map((attr) => attr.dumpToGSQLJson()),
      Config: this.config
    };

    if (this.primaryId.isPrimaryKey && this.attributes.findIndex((attr) => attr.name === this.primaryId.name) === -1) {
      vertexJson.Attributes.unshift(this.primaryId.dumpToGSQLJson());
    }

    // EmbeddingAttributes
    if (this.embeddingAttributes.length > 0) {
      vertexJson.EmbeddingAttributes = this.embeddingAttributes.map((attr) => attr.dumpToGSQLJson());
    }

    // Update IsLocal flag
    if (this.isLocal) {
      vertexJson.IsLocal = true;
    }

    if (this.usage) {
      vertexJson.Usage = this.usage;
    }

    if (this.compositeKeys) {
      vertexJson.CompositeKeys = this.compositeKeys;
    }

    return vertexJson;
  }

  /**
   * Get the vertex visualization information.
   *
   * @returns {NodeVis}
   * @memberof Vertex
   */
  getVis(): NodeVis {
    const vis: NodeVis = {
      attributes: {},
      vertex_id: this.name,
      vertex_type: this.name,
      other: {}
    };
    // primary id, attributes name and type
    vis.attributes['(PRIMARY ID) ' + this.primaryId.name] = this.primaryId.type.name;
    this.attributes.forEach((attr) => vis.attributes[attr.name] = attr.type.toString());
    // visualization style
    if (this.style !== undefined) {
      vis.style = this.style.dumpToDBJson();
      vis.other['fixed'] = true;
    }
    return vis;
  }

  /**
   * Get the embedding attribute from name.
   *
   * @returns {EmbeddingAttribute}
   * @memberof Vertex
   */
  getEmbeddingAttribute(name: string): EmbeddingAttribute {
    const embeddingAttribute = this.embeddingAttributes.find((attr) => attr.name === name);
    if (embeddingAttribute === undefined) {
      throw new GraphStudioError(`Embedding attribute "${name}" not found.`);
    }
    return embeddingAttribute;
  }

  /**
   * Semantic check vertex type to be valid vertex type.
   *
   * @returns {ValidateResult}
   * @memberof Vertex
   */
  semanticCheck(): ValidateResult {
    // 1. Invalid vertex type name.
    let validateName = FormatValidator.isName(this.name);
    if (!validateName.success) {
      return {
        success: false,
        message: `Invalid vertex name "${this.name}". ` + validateName.message
      };
    }
    // 2. Primary ID must has valid name
    validateName = FormatValidator.isName(this.primaryId.name);
    if (!validateName.success) {
      return {
        success: false,
        message: `Invalid PrimaryID name "${this.primaryId.name}". ` + validateName.message
      };
    }
    // 3. Primary ID name cannot be same with vertex type name.
    if (this.primaryId.name === this.name) {
      return {
        success: false,
        message: `Vertex type name "${this.name}" cannot be used as its primary ID name.`
      };
    }
    // 4. Attribute name cannot be same with vertex type name.
    for (const attr of this.attributes) {
      if (attr.name === this.name) {
        return {
          success: false,
          message: `Vertex type name "${this.name}" cannot be used as its attributes' names.`
        };
      }
    }
    // 5. Attribute name cannot be duplicated.
    const names: Set<string> = new Set<string>();
    for (let i = 0; i < this.attributes.length; i++) {
      if (names.has(this.attributes[i].name)) {
        return {
          success: false,
          message: `More than one attributes have name "${this.attributes[i].name}".`
        };
      }
      names.add(this.attributes[i].name);
    }

    // 6. Attributes must be valid.
    for (const attribute of this.attributes) {
      const validResult = attribute.semanticCheck();
      if (!validResult.success) {
        return validResult;
      }
    }

    // 7. If primary id is used as an attribute, its name cannot be used as annther attribute's name.
    if (this.config.PRIMARY_ID_AS_ATTRIBUTE === true) {
      for (const attr of this.attributes) {
        if (attr.name === this.primaryId.name) {
          return {
            success: false,
            message: `Primary ID "${this.primaryId.name}" is used as an attribute, ` +
              `so "${this.primaryId.name}" cannot be used as another attribute's name.`
          };
        }
      }
    }

    // 8. If primary key is used, its name cannot be used as annther attribute's name.
    if (this.primaryId.isPrimaryKey) {
      for (const attr of this.attributes) {
        if (attr.name === this.primaryId.name) {
          return {
            success: false,
            message: `Primary Key "${this.primaryId.name}" is used, ` +
              `so "${this.primaryId.name}" cannot be used as another attribute's name.`
          };
        }
      }
    }

    // 9. Currently GraphStudio doesn't support vertex created using COMPOSITE KEY.
    if (this.compositeKeys && this.compositeKeys.length > 0) {
      return {
        success: false,
        message:
          'Composite Key is not supported. ' +
          'You must use PRIMARY_ID or single PRIMARY KEY when creating your schema.'
      };
    }

    // 10. Embedding attribute name cannot be same with vertex type name.
    for (const attr of this.embeddingAttributes) {
      if (attr.name === this.name) {
        return {
          success: false,
          message: `Vertex type name "${this.name}" cannot be used as its embedding attributes' names.`
        };
      }
    }

    // 11. Embedding attributes must be valid.
    for (const embeddingAttribute of this.embeddingAttributes) {
      const validResult = embeddingAttribute.semanticCheck();
      if (!validResult.success) {
        return validResult;
      }
    }

    // 12. Embedding attribute name cannot be duplicated.
    const embeddingNames: Set<string> = new Set<string>();
    for (let i = 0; i < this.embeddingAttributes.length; i++) {
      if (embeddingNames.has(this.embeddingAttributes[i].name)) {
        return {
          success: false,
          message: `More than one embedding attributes have name "${this.embeddingAttributes[i].name}".`
        };
      }
      embeddingNames.add(this.embeddingAttributes[i].name);
    }

    // 13. Embedding attribute name cannot be duplicated with vertex primary id.
    for (let i = 0; i < this.embeddingAttributes.length; i++) {
      if (this.embeddingAttributes[i].name === this.primaryId.name) {
        return {
          success: false,
          message: `Primary ID "${this.primaryId.name}" cannot be used as its embedding attributes' names.`
        };
      }
    }

    // 14. Embedding attribute name cannot be duplicated with vertex attributes.
    for (let i = 0; i < this.embeddingAttributes.length; i++) {
      if (names.has(this.embeddingAttributes[i].name)) {
        return {
          success: false,
          message: `Attribute's name "${this.embeddingAttributes[i].name}" cannot be used as its embedding attributes' names.`
        };
      }
    }

    return {
      success: true
    };
  }
}
