import { GraphStudioError } from '../error';

import { EmbeddingAttribute, GSQLEmbeddingAttributeJson } from './embedding-attribute.model';

describe('EmbeddingAttributeModel', () => {

  it('should test clone and result should have the same values.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute().loadFromGSQLJson(mockEmbeddingAttribute);
    expect(embeddingAttribute.clone()).toEqual(embeddingAttribute);

    const mockEmbeddingAttribute2: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute2',
      EmbeddingAttrId: 2,
      Dimension: 256,
      Metric: 'L2',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute2 = new EmbeddingAttribute().loadFromGSQLJson(mockEmbeddingAttribute2);
    expect(embeddingAttribute2.clone()).toEqual(embeddingAttribute2);
  });

  it('should throw error because it is invalid embedding attribute name.', () => {
    const mockEmbeddingAttributeInvalidName: GSQLEmbeddingAttributeJson = {
      Name: '#wrong name',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    expect(() => embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttributeInvalidName))
      .toThrow(new GraphStudioError('Invalid embedding attribute name "#wrong name". ' +
        'Valid name starts with a letter, followed by letters or digits.'));
  });

  it('should contain all embedding attribute properties', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.name).toEqual('my_embedding_attribute');
    expect(embeddingAttribute.embeddingAttrId).toEqual(1);
    expect(embeddingAttribute.dimension).toEqual(128);
    expect(embeddingAttribute.metric).toEqual('COSINE');
    expect(embeddingAttribute.indexType).toEqual('HNSW');
    expect(embeddingAttribute.type).toEqual('FLOAT');

    const embeddingAttributeJson = embeddingAttribute.dumpToGSQLJson();
    expect(embeddingAttributeJson.Name).toEqual('my_embedding_attribute');
    expect(embeddingAttributeJson.EmbeddingAttrId).toEqual(1);
    expect(embeddingAttributeJson.Dimension).toEqual(128);
    expect(embeddingAttributeJson.Metric).toEqual('COSINE');
    expect(embeddingAttributeJson.IndexType).toEqual('HNSW');
    expect(embeddingAttributeJson.DataType).toEqual('FLOAT');
    expect(Object.keys(embeddingAttributeJson).length).toEqual(6);
  });

  it('should not pass semantic check because it is invalid embedding attribute name.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'valid_name',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);
    embeddingAttribute.name = '#wrong name';

    expect(embeddingAttribute.semanticCheck().success).toBeFalsy();
    expect(embeddingAttribute.semanticCheck().message).toBe('Invalid attribute name "#wrong name". ' +
      'Valid name starts with a letter, followed by letters or digits.');
  });

  it('should not pass semantic check because it is invalid metric.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'INVALID_METRIC',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeFalsy();
    expect(embeddingAttribute.semanticCheck().message).toBe('METRIC only supports COSINE, L2, IP for embedding attribute "my_embedding_attribute".');
  });

  it('should not pass semantic check because it is invalid data type.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'INVALID_TYPE'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeFalsy();
    expect(embeddingAttribute.semanticCheck().message).toBe('DATA TYPE only supports FLOAT for embedding attribute "my_embedding_attribute".');
  });

  it('should not pass semantic check because it is invalid index type.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'COSINE',
      IndexType: 'INVALID_INDEX',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeFalsy();
    expect(embeddingAttribute.semanticCheck().message).toBe('INDEX TYPE only supports HNSW for embedding attribute "my_embedding_attribute".');
  });

  it('should not pass semantic check because dimension is zero.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 0,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeFalsy();
    expect(embeddingAttribute.semanticCheck().message).toBe('DIMENSION must be a positive integer and less than 32768 for embedding attribute "my_embedding_attribute".');
  });

  it('should not pass semantic check because dimension is negative.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: -1,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeFalsy();
    expect(embeddingAttribute.semanticCheck().message).toBe('DIMENSION must be a positive integer and less than 32768 for embedding attribute "my_embedding_attribute".');
  });

  it('should not pass semantic check because dimension exceeds maximum.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 32769,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeFalsy();
    expect(embeddingAttribute.semanticCheck().message).toBe('DIMENSION must be a positive integer and less than 32768 for embedding attribute "my_embedding_attribute".');
  });

  it('should pass semantic check with valid COSINE metric.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeTruthy();
  });

  it('should pass semantic check with valid L2 metric.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 256,
      Metric: 'L2',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeTruthy();
  });

  it('should pass semantic check with valid IP metric.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 512,
      Metric: 'IP',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeTruthy();
  });

  it('should pass semantic check with valid HNSW index type.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeTruthy();
  });

  it('should pass semantic check with valid FLOAT data type.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeTruthy();
  });

  it('should pass semantic check with valid dimension at minimum.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 1,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeTruthy();
  });

  it('should pass semantic check with valid dimension at maximum.', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'my_embedding_attribute',
      EmbeddingAttrId: 1,
      Dimension: 32768,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeTruthy();
  });

  it('should load from and dump to embedding attribute correctly', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'test_embedding',
      EmbeddingAttrId: 42,
      Dimension: 1024,
      Metric: 'L2',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute().loadFromGSQLJson(mockEmbeddingAttribute);
    expect(embeddingAttribute.name).toBe('test_embedding');
    expect(embeddingAttribute.embeddingAttrId).toBe(42);
    expect(embeddingAttribute.dimension).toBe(1024);
    expect(embeddingAttribute.metric).toBe('L2');
    expect(embeddingAttribute.indexType).toBe('HNSW');
    expect(embeddingAttribute.type).toBe('FLOAT');

    const embeddingAttributeJson = embeddingAttribute.dumpToGSQLJson();
    expect(embeddingAttributeJson.Name).toBe('test_embedding');
    expect(embeddingAttributeJson.EmbeddingAttrId).toBe(42);
    expect(embeddingAttributeJson.Dimension).toBe(1024);
    expect(embeddingAttributeJson.Metric).toBe('L2');
    expect(embeddingAttributeJson.IndexType).toBe('HNSW');
    expect(embeddingAttributeJson.DataType).toBe('FLOAT');
  });

  it('should pass semantic check with all valid properties', () => {
    const mockEmbeddingAttribute: GSQLEmbeddingAttributeJson = {
      Name: 'valid_embedding',
      EmbeddingAttrId: 1,
      Dimension: 128,
      Metric: 'COSINE',
      IndexType: 'HNSW',
      DataType: 'FLOAT'
    };

    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.loadFromGSQLJson(mockEmbeddingAttribute);

    expect(embeddingAttribute.semanticCheck().success).toBeTruthy();
  });
}); 