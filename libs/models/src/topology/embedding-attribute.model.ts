import { GraphStudioError } from "../error";
import { FormatValidator, ValidateResult } from "../utils";

export const supportedEmbeddingAttributeMetrics: string[] = [
  'COSINE',
  'L2',
  'IP',
];

export const supportedEmbeddingAttributeIndexTypes: string[] = [
  'HNSW',
];

export const supportedEmbeddingAttributeTypes: string[] = [
  'FLOAT',
];

export const supportedEmbeddingAttributeMaxDimensions: number = 32768;

/**
 * GSQL Embedding Attribute json representation interface following the format:
 *   {
 *     "Name": string,
 *     "EmbeddingAttrId": number,
 *     "Dimension": number,
 *     "Metric": string
 *     "IndexType": string,
 *     "DataType": string,
 *   }
 *
 * @export
 * @interface GSQLAttributeJson
 */
export interface GSQLEmbeddingAttributeJson {
  Name: string;
  Dimension: number;
  Metric: string;
  IndexType: string;
  DataType: string;
  EmbeddingAttrId?: number;
}

/**
 * Attribute describes a vertex attribute or edge attribute, including the
 * attribute's name, type and additional properties like default value and alias.
 *
 * @export
 * @class Attribute
 */
export class EmbeddingAttribute {
  name: string;
  embeddingAttrId: number;
  dimension: number;
  metric: string;
  indexType: string;
  type: string;

  /**
   * Creates an instance of EmbeddingAttribute.
   *
   * @memberof EmbeddingAttribute
   */
  constructor() {
    this.name = "";
    this.embeddingAttrId = 0;
    this.dimension = 0;
    this.metric = "";
    this.indexType = "";
    this.type = "";
  }

  /**
   * Clone the embedding attribute.
   *
   * @returns {EmbeddingAttribute}
   * @memberof EmbeddingAttribute
   */
  clone(): EmbeddingAttribute {
    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.name = this.name;
    embeddingAttribute.embeddingAttrId = this.embeddingAttrId;
    embeddingAttribute.dimension = this.dimension;
    embeddingAttribute.metric = this.metric;
    embeddingAttribute.indexType = this.indexType;
    embeddingAttribute.type = this.type;
    return embeddingAttribute;
  }

  /**
   * Load Attribute from GSQL Embedding Attribute json representation.
   *
   * @param {GSQLEmbeddingAttributeJson} embeddingAttributeJson GSQL Embedding Attribute json representation
   * @returns {Attribute}
   * @memberof Attribute
   */
  loadFromGSQLJson(embeddingAttributeJson: GSQLEmbeddingAttributeJson): EmbeddingAttribute {
    // AttributeName
    this.name = embeddingAttributeJson.Name;
    const validateName = FormatValidator.isName(this.name);
    if (!validateName.success) {
      throw new GraphStudioError(
        `Invalid embedding attribute name "${this.name}". ` + validateName.message
      );
    }

    // EmbeddingAttrId
    this.embeddingAttrId = embeddingAttributeJson.EmbeddingAttrId;

    // Dimension
    this.dimension = embeddingAttributeJson.Dimension;

    // Metric
    this.metric = embeddingAttributeJson.Metric;

    // IndexType
    this.indexType = embeddingAttributeJson.IndexType;

    // DataType
    this.type = embeddingAttributeJson.DataType;

    return this;
  }

  /**
   * Dump Embedding Attribute into GSQL Embedding Attribute json representation.
   *
   * @returns {GSQLEmbeddingAttributeJson}
   * @memberof EmbeddingAttribute
   */
  dumpToGSQLJson(): GSQLEmbeddingAttributeJson {
    const embeddingAttributeJson: GSQLEmbeddingAttributeJson = {
      Name: this.name,
      EmbeddingAttrId: this.embeddingAttrId,
      Dimension: this.dimension,
      Metric: this.metric,
      IndexType: this.indexType,
      DataType: this.type,
    };
    return embeddingAttributeJson;
  }

  /**
   * Semantic check to be valid attribute.
   *
   * @returns {ValidateResult}
   * @memberof EmbeddingAttribute
   */
  semanticCheck(): ValidateResult {
    // 1. Attribute name must be valid name.
    const validateName = FormatValidator.isName(this.name);
    if (!validateName.success) {
      return {
        success: false,
        message: `Invalid attribute name "${this.name}". ${validateName.message}`,
      };
    }

    // 2. Embedding Attribute metric must be valid metric.
    if (!supportedEmbeddingAttributeMetrics.includes(this.metric)) {
      return {
        success: false,
        message: `METRIC only supports ${supportedEmbeddingAttributeMetrics.join(', ')} for embedding attribute "${this.name}".`,
      };
    }

    // 3. Embedding Attribute type must be valid type.
    if (!supportedEmbeddingAttributeTypes.includes(this.type)) {
      return {
        success: false,
        message: `DATA TYPE only supports ${supportedEmbeddingAttributeTypes.join(', ')} for embedding attribute "${this.name}".`,
      };
    }

    // 4. Embedding Attribute index type must be valid index.
    if (!supportedEmbeddingAttributeIndexTypes.includes(this.indexType)) {
      return {
        success: false,
        message: `INDEX TYPE only supports ${supportedEmbeddingAttributeIndexTypes.join(', ')} for embedding attribute "${this.name}".`,
      };
    }

    // 5. Embedding attribute dimension must be valid number.
    if (this.dimension <= 0 || this.dimension > supportedEmbeddingAttributeMaxDimensions) {
      return {
        success: false,
        message: `DIMENSION must be a positive integer and less than ${supportedEmbeddingAttributeMaxDimensions} for embedding attribute "${this.name}".`,
      };
    }

    return {
      success: true,
    };
  }
}
