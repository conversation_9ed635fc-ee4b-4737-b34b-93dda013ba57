import { GSQLLoadTo<PERSON>dge<PERSON>son, GSQLLoadToVectorJson, GSQLLoadToVertexJson } from './gsql-loading-job.interface';
import { LoadingMappingLogic, SourceType } from './loading-mapping.model';
import { LoadToEdgeData } from './load-to-edge.model';
import { LoadToVertexData } from './load-to-vertex.model';
import { LoadToVectorData } from './load-to-vector.model';

/**
 * Common business logic for load to vertex and load to edge.
 *
 * @export
 * @class LoadToEntityLogic
 */
export class LoadToEntityLogic {
  /**
   * Check if the GSQL load to vertex | edge json is valid json, i.e., not containing
   * unsupported features.
   *
   * @static
   * @param {(GSQLLoadToVertexJson | GSQLLoadToEdgeJson | GSQLLoadToVectorJson)} loadToEntityJson
   * @returns {{
   *     success: boolean,
   *     message?: string
   *   }}
   * @memberof LoadToEntityLogic
   */
  static validationCheck(loadToEntityJson: GSQLLoadToVertexJson | GSQLLoadToEdgeJson | GSQLLoadToVectorJson): {
    success: boolean,
    message?: string
  } {
    // 1. DataSource is not Online
    if (loadToEntityJson.DataSource.Type !== 'FileVar') {
      return {
        success: false,
        message: `Data source type "${loadToEntityJson.DataSource.Type}" ` +
        'is not supported in Graph Studio.'
      };
    }

    // 3. Options is not empty
    if (('Options' in loadToEntityJson) && loadToEntityJson.Options !== '') {
      return {
        success: false,
        message: '"Options" is not supported in Graph Studio.'
      };
    }

    // 4. Recursively check each mapping item is supported types
    for (const oneColumnMapping of loadToEntityJson.Mappings) {
      const validationCheckOneColumnMapping =
        LoadingMappingLogic.validationCheck(oneColumnMapping);
      if (!validationCheckOneColumnMapping.success) {
        return validationCheckOneColumnMapping;
      }
    }

    return {
      success: true
    };
  }

  /**
   * Semantic check if the load to vertex | edge statement has semantic errors.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData | LoadToVectorData)} loadToEntity
   * @param {string[]} header
   * @returns {{
   *     success: boolean,
   *     message: string
   *   }}
   * @memberof LoadToEntityLogic
   */
  static semanticCheck(
    loadToEntity: LoadToVertexData | LoadToEdgeData | LoadToVectorData,
    header: string[]
  ): {
    success: boolean,
    message: string
  } {
    // 1. Token function number and middle style number don't match
    if (loadToEntity.mappingWidgets.length !== loadToEntity.style.middlePositions.length) {
      return {
        success: false,
        message: 'Token function number and token function style number do not match.'
      };
    }

    // 2. Loading mapping refer to a data source column out of boundary
    for (const mapping of loadToEntity.mappings) {
      if (mapping.sourceType === SourceType.DataSourceColumn && mapping.index >= header.length) {
        return {
          success: false,
          message: 'Data mapped to a column outside data source columns.'
        };
      }
    }

    for (const widget of loadToEntity.mappingWidgets) {
      for (const mapping of widget.params) {
        if (
          mapping.sourceType === SourceType.DataSourceColumn &&
          mapping.index >= header.length
        ) {
          return {
            success: false,
            message: 'Token function parameter mapped to a column outside data source columns.'
          };
        }
      }
    }

    // pass semantic check
    return {
      success: true,
      message: ''
    };
  }
}
