import { Attribute, Edge, EmbeddingAttribute, Graph, Vertex } from '../topology';
import { DataFormat } from './data-set.interface';

import { DBLoadingStatementStyle } from './db-loading-job.interface';
import { GSQLLoadToVectorJson } from './gsql-loading-job.interface';
import { LoadToVectorLogic, MAPPING_NUMBER } from './load-to-vector.model';
import { SourceType } from './loading-mapping.model';

describe('GSQLLoadToVectorModel', () => {
  let dbLoadingStatementStyle: DBLoadingStatementStyle;
  let mockGsqlLoadToVectorJson: GSQLLoadToVectorJson;

  beforeEach(() => {
    dbLoadingStatementStyle = {
      sourcePosition: {
        x: 0.2,
        y: 0.5
      },
      targetPosition: {
        x: 0.8,
        y: 0.5
      },
      middlePositions: []
    };

    mockGsqlLoadToVectorJson = {
      Type: 'Vector',
      UsingClauses: {},
      Mappings: [
        {
          Type: 'SrcColIndex',
          Value: 0
        },
        {
          Type: 'SrcColIndex',
          Value: 1
        }
      ],
      VertexName: 'person',
      VectorName: 'embedding',
      DataSource: {
        Type: 'Online'
      }
    };
  });

  it('should clone load to vector successfully', () => {
    const loadToVectorOrigin = LoadToVectorLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVectorJson,
      dbLoadingStatementStyle,
      [],
    );
    const loadToVector = LoadToVectorLogic.clone(loadToVectorOrigin);
    loadToVectorOrigin.vertexName = 'movie';
    expect(loadToVectorOrigin.vertexName).toBe('movie');
    expect(loadToVector.vertexName).toBe('person');
    expect(loadToVector.vectorName).toBe('embedding');
    expect(loadToVector.style.sourcePosition.x).toBe(0.2);
    expect(loadToVector.style.targetPosition.y).toBe(0.5);
    expect(loadToVector.mappings.length).toBe(2);
    expect(loadToVector.mappings[0].sourceType).toBe(
      SourceType.DataSourceColumn
    );
    expect(loadToVector.mappings[0].index).toBe(0);
    expect(loadToVector.mappings[1].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadToVector.mappings[1].index).toBe(1);
    expect(loadToVector.mappingWidgets.length).toBe(0);
  });

  it('should fail semantic check because vertex is not in schema', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'v1';
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'v1', to: 'v1' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to vector
    const loadToVector = LoadToVectorLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVectorJson,
      dbLoadingStatementStyle,
      [],
    );

    const semanticCheckResult = LoadToVectorLogic.semanticCheck(
      loadToVector,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Vertex type "person" doesn\'t exist in graph schema.'
    );
  });

  it('should fail semantic check because vector attribute is not in schema', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.name = 'different_embedding';
    vertex.embeddingAttributes.push(embeddingAttribute);
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to vector
    const loadToVector = LoadToVectorLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVectorJson,
      dbLoadingStatementStyle,
      [],
    );

    const semanticCheckResult = LoadToVectorLogic.semanticCheck(
      loadToVector,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Vector attribute "embedding" doesn\'t exist in vertex type "person".'
    );
  });

  it('should fail semantic check because mapping number doesn\'t match', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.name = 'embedding';
    vertex.embeddingAttributes.push(embeddingAttribute);
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to vector with wrong number of mappings
    const loadToVector = LoadToVectorLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVectorJson,
      dbLoadingStatementStyle,
      [],
    );
    // Remove one mapping to make it wrong
    loadToVector.mappings.pop();

    const semanticCheckResult = LoadToVectorLogic.semanticCheck(
      loadToVector,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Vector data loading mapping number doesn\'t match.'
    );
  });

  it('should fail semantic check because primary id mapping cannot be default', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.name = 'embedding';
    vertex.embeddingAttributes.push(embeddingAttribute);
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to vector
    const loadToVector = LoadToVectorLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVectorJson,
      dbLoadingStatementStyle,
      [],
    );
    loadToVector.mappings[0].sourceType = SourceType.Default;

    const semanticCheckResult = LoadToVectorLogic.semanticCheck(
      loadToVector,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Need to map data column to vertex primary id.'
    );
  });

  it('should fail semantic check because split separator is missing', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.name = 'embedding';
    vertex.embeddingAttributes.push(embeddingAttribute);
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to vector
    const loadToVector = LoadToVectorLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVectorJson,
      dbLoadingStatementStyle,
      [],
    );
    // Set up split without separator
    loadToVector.mappings[1].sourceType = SourceType.DataSourceColumn;
    loadToVector.mappings[1].index = 0;
    loadToVector.mappings[1].isSplit = true;
    loadToVector.mappings[1].splitSeparator = undefined;

    const semanticCheckResult = LoadToVectorLogic.semanticCheck(
      loadToVector,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Split separator must be specified.'
    );
  });

  it('should fail semantic check because loading mapping refer to a data source column don\'t match', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.name = 'embedding';
    vertex.embeddingAttributes.push(embeddingAttribute);
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to vector
    const loadToVector = LoadToVectorLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVectorJson,
      dbLoadingStatementStyle,
      [],
    );

    let semanticCheckResult = LoadToVectorLogic.semanticCheck(
      loadToVector,
      ['name'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Data mapped to a column outside data source columns.'
    );

    semanticCheckResult = LoadToVectorLogic.semanticCheck(
      loadToVector,
      ['name'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Data mapped to a column outside data source columns.'
    );
  });

  it('should create one load-to-vector statement correctly', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.name = 'embedding';
    vertex.embeddingAttributes.push(embeddingAttribute);
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        }
      })
    );
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'movie' }];
    const vertex2 = new Vertex();
    vertex2.name = 'movie';
    vertex2.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'STRING'
        }
      })
    );
    schema.vertexTypes.push(vertex);
    schema.vertexTypes.push(vertex2);
    schema.edgeTypes.push(edge);

    // Create one load-to-vector statement
    const loadToVector = LoadToVectorLogic.createLoadToVector(
      schema.getVertex('person'),
      embeddingAttribute,
      {
        HEADER: 'FALSE',
        EOL: '\n',
        SEPARATOR: ','
      }
    );

    expect(loadToVector.vertexName).toBe('person');
    expect(loadToVector.vectorName).toBe('embedding');
    expect(loadToVector.mappingWidgets.length).toBe(0);
    expect(loadToVector.mappings.length).toBe(MAPPING_NUMBER);
    loadToVector.mappings.forEach(mapping => {
      expect(mapping.sourceType).toBe(SourceType.Default);
    });
    expect(loadToVector.style.sourcePosition.x).toBe(30);
    expect(loadToVector.style.sourcePosition.y).toBe(300);
    expect(loadToVector.style.targetPosition.x).toBe(650);
    expect(loadToVector.style.targetPosition.y).toBe(300);
    expect(loadToVector.style.middlePositions.length).toBe(0);
  });

  it('should pass semantic check with valid load-to-vector data', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.name = 'embedding';
    vertex.embeddingAttributes.push(embeddingAttribute);
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to vector
    const loadToVector = LoadToVectorLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVectorJson,
      dbLoadingStatementStyle,
      [],
    );

    const semanticCheckResult = LoadToVectorLogic.semanticCheck(
      loadToVector,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeTruthy();
  });

  it('should handle dumpToGSQLAndDB correctly', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const embeddingAttribute = new EmbeddingAttribute();
    embeddingAttribute.name = 'embedding';
    vertex.embeddingAttributes.push(embeddingAttribute);
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to vector
    const loadToVector = LoadToVectorLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVectorJson,
      dbLoadingStatementStyle,
      [],
    );

    const [gsqlLoadToVector, loadingStyle] = LoadToVectorLogic.dumpToGSQLAndDB(
      loadToVector,
      DataFormat.CSV,
      []
    );

    expect(gsqlLoadToVector.Type).toBe('Vector');
    expect(gsqlLoadToVector.VertexName).toBe('person');
    expect(gsqlLoadToVector.VectorName).toBe('embedding');
    expect(gsqlLoadToVector.DataSource.Type).toBe('Online');
    expect(gsqlLoadToVector.Mappings.length).toBe(2);
    expect(loadingStyle.sourcePosition.x).toBe(0.2);
    expect(loadingStyle.targetPosition.y).toBe(0.5);
  });
});
