import { parseExpr<PERSON><PERSON> } from '../expression';
import { EmbeddingAttribute, Graph, Vertex } from '../topology';
import { HelperFunctions } from '../utils';

import { DBPositionStyle, DBLoadingStatementStyle, DBMappingWidgetIndex } from './db-loading-job.interface';
import { GSQLLoadToVectorJson, GSQLDataSourceJson } from './gsql-loading-job.interface';
import {
  LoadingMappingLogic, OneColumnMapping,
  SourceType, TokenFunction, MapWidget, TupleWidget
} from './loading-mapping.model';
import { LoadToEntityLogic } from './load-to-entity-logic.model';
import { DataFieldSchema, DataFormat } from './data-set.interface';
import { cloneDeep } from 'lodash';

/**
 * Describes one load to vector statement.
 * - vertexName: loading target vertex type
 * - vectorName: loading target vector name
 * - usingClauses: the using clauses
 * - hasOptions: the loading job has options or not
 * - options: if the loading job has options, record the options as a string here
 * - mappingWidgets: the mapping widgets (UDF, Map, Tuple, etc) used in the loading statement
 * - mappings: the loading mapping of primary id and attributes. 1st one is primary id mapping
 * - style: records the positions (and other styles in the future) of data source, target and
 *          middle items like token functions, etc for rendering in rhs panel.
 * - usingClauses: the using clauses.
 * - dataSource: the data source.
 *
 * @export
 * @interface LoadToVectorData
 */
export interface LoadToVectorData {
  vertexName: string;
  vectorName: string;
  // NOTE: these props might be supported in the future.
  // hasOptions: boolean;
  // options: string;
  mappingWidgets: (TokenFunction | MapWidget | TupleWidget)[];
  mappings: OneColumnMapping[];
  style: DBLoadingStatementStyle;
  usingClauses: {[propName: string]: string};
  dataSource: GSQLDataSourceJson;
}

export const MAPPING_NUMBER = 2;

/**
 * Provides APIs to handle load to vector related business logic.
 *
 * @export
 * @class LoadToVectorLogic
 */
export class LoadToVectorLogic {
  /**
   * Clone load to vector data.
   *
   * @static
   * @param {LoadToVectorData} data
   * @returns {LoadToVectorData}
   * @memberof LoadToVectorLogic
   */
  static clone(data: LoadToVectorData): LoadToVectorData {
    return JSON.parse(JSON.stringify(data));
  }

  /**
   * Load information from GSQL load to vector json and database json into load to vector data.
   * Should do validation check before invoking this function.
   *
   * @static
   * @param {GSQLLoadToVectorJson} gsqlLoadToVectorJson
   * @param {DBLoadingStatementStyle} loadingStatementStyle
   * @param {DataFieldSchema[]} dataSchema
   * @returns {LoadToVectorData}
   * @memberof LoadToVectorLogic
   */
  static loadFromGSQLAndDB(
    gsqlLoadToVectorJson: GSQLLoadToVectorJson,
    loadingStatementStyle: DBLoadingStatementStyle,
    dataSchema: DataFieldSchema[]
  ): LoadToVectorData {
    const loadToVectorData: LoadToVectorData = {
      vertexName: gsqlLoadToVectorJson.VertexName,
      vectorName: gsqlLoadToVectorJson.EmbeddingAttribute?.Name ?? gsqlLoadToVectorJson.VectorName,
      style: loadingStatementStyle,
      mappings: [],
      mappingWidgets: [],
      usingClauses: gsqlLoadToVectorJson.UsingClauses,
      dataSource: gsqlLoadToVectorJson.DataSource,
    };

    if (loadingStatementStyle.mappingWidgetIndices) {
      let maxIndex = 0;
      const visited = new Set<number>();
      const tempMiddlePositions = [];
      for (let { index } of loadingStatementStyle.mappingWidgetIndices) {
        if (visited.has(index)) {
          continue;
        }
        visited.add(index);
        maxIndex = Math.max(maxIndex, index);
        tempMiddlePositions.push(loadingStatementStyle.middlePositions[index]);
      }
      for (let i = 0; i <= maxIndex; i++) {
        if (!visited.has(i)) {
          loadingStatementStyle.mappingWidgetIndices.forEach(item => {
            if (item.index > i) {
              item.index--;
            }
          })
        }
      }
      loadingStatementStyle.middlePositions = tempMiddlePositions;
    }

    for (const oneColumnMapping of gsqlLoadToVectorJson.Mappings) {
      LoadingMappingLogic.loadMappingFromGSQLMappingJson(
        oneColumnMapping,
        loadToVectorData.mappings,
        loadToVectorData.mappingWidgets,
        dataSchema,
        cloneDeep(loadingStatementStyle.mappingWidgetIndices || []),
      );
    }

    return loadToVectorData;
  }

  /**
   * Dump in-memory load-to-vector object into GSQL load-to-vector json and database style json.
   *
   * @static
   * @param {LoadToVectorData} loadToVector
   * @param {DataFormat} dataFormat
   * @param {DataFieldSchema[]} dataSchema
   * @returns {[GSQLLoadToVectorJson, DBLoadingStatementStyle]}
   * @memberof LoadToVectorLogic
   */
  static dumpToGSQLAndDB(
    loadToVector: LoadToVectorData,
    dataFormat: DataFormat,
    dataSchema: DataFieldSchema[],
  ): [GSQLLoadToVectorJson, DBLoadingStatementStyle] {
    // Init
    const gsqlLoadToVector: GSQLLoadToVectorJson = {
      Type: 'Vector',
      VertexName: loadToVector.vertexName,
      VectorName: loadToVector.vectorName,
      DataSource: loadToVector.dataSource,
      Mappings: [],
      UsingClauses: loadToVector.usingClauses,
    };

    // Collect where clause for gsql if it is defined.
    if (loadToVector.style.whereClauseJson) {
      gsqlLoadToVector.WhereClause = parseExprJson(loadToVector.style.whereClauseJson).toString();
    }

    // Collect loading mapping
    const tokenFunctionPositions: DBPositionStyle[] = [];
    const mappingWidgetIndices: DBMappingWidgetIndex[] = [];
    loadToVector.mappings.forEach(mapping => {
      LoadingMappingLogic.dumpToGSQLLoadingMapping(
        gsqlLoadToVector.Mappings,
        mapping,
        loadToVector.mappingWidgets,
        loadToVector.style,
        tokenFunctionPositions,
        mappingWidgetIndices,
        dataFormat,
        dataSchema,
      );
    });

    const loadingStyle: DBLoadingStatementStyle = {
      sourcePosition: loadToVector.style.sourcePosition,
      targetPosition: loadToVector.style.targetPosition,
      middlePositions: tokenFunctionPositions,
      mappingWidgetIndices: mappingWidgetIndices,
    };

    // Collect where clause styles if it is defined.
    if (loadToVector.style.whereClauseJson) {
      loadingStyle.whereClauseJson = loadToVector.style.whereClauseJson;
    }

    return [
      gsqlLoadToVector,
      loadingStyle
    ];
  }

  /**
   * Semantic check if the load to vector statement has semantic errors.
   *
   * @static
   * @param {LoadToVectorData} loadToVector
   * @param {string[]} header
   * @param {Graph} schema
   * @returns {{
   *     success: boolean,
   *     message: string
   *   }}
   * @memberof LoadToVectorLogic
   */
  static semanticCheck(
    loadToVector: LoadToVectorData,
    header: string[],
    schema: Graph
  ): {
    success: boolean;
    message: string;
  } {
    let foundType = false;
    let foundVectorAttribute = false;
    schema.vertexTypes.forEach(vertexType => {
      if (vertexType.name === loadToVector.vertexName) {
        foundType = true;
        vertexType.embeddingAttributes.forEach(embeddingAttribute => {
          if (embeddingAttribute.name === loadToVector.vectorName) {
            foundVectorAttribute = true;
          }
        });
      }
    });

    // 1. Vertex name not exist in schema
    if (!foundType) {
      return {
        success: false,
        message: `Vertex type "${loadToVector.vertexName}" doesn't exist in graph schema.`
      };
    }

    // 2. Vector attribute name not exist in schema
    if (!foundVectorAttribute) {
      return {
        success: false,
        message: `Vector attribute "${loadToVector.vectorName}" doesn't exist in vertex type "${loadToVector.vertexName}".`
      };
    }

    // 3. Loading mapping must with same number as the embedding attribute plus primary id
    if (loadToVector.mappings.length !== MAPPING_NUMBER) {
      return {
        success: false,
        message: `Vector data loading mapping number doesn't match.`
      };
    }

    // 4. Primary id cannot be default
    if (loadToVector.mappings[0].sourceType === SourceType.Default) {
      return {
        success: false,
        message: 'Need to map data column to vertex primary id.'
      };
    }

    // 5. IsSplit needs SplitSeparator
    if (
      loadToVector.mappings[1].sourceType !== SourceType.Default &&
      loadToVector.mappings[1].index !== undefined &&
      loadToVector.mappings[1].index >= 0 &&
      loadToVector.mappings[1].isSplit
    ) {
      if (!loadToVector.mappings[1].splitSeparator) {
        return {
          success: false,
          message: 'Split separator must be specified.',
        };
      }
    }

    // Do common semantic checks same with loadToEdge
    return LoadToEntityLogic.semanticCheck(loadToVector, header);
  }

  /**
   * Create one load-to-vector statement.
   *
   * @static
   * @param {Vertex} vertex
   * @param {EmbeddingAttribute} embeddingAttribute
   * @param {{[propName: string]: string}} usingClauses
   * @returns {LoadToVectorData}
   * @memberof LoadToVectorLogic
   */
  static createLoadToVector(
    vertex: Vertex,
    embeddingAttribute: EmbeddingAttribute,
    usingClauses: {[propName: string]: string}
  ): LoadToVectorData {
    // For brand new load-to-vector statement, set data mapping to primary id and
    // attributes all as 'Default'
    const mappings: OneColumnMapping[] = [];
    for (let i = 0; i < MAPPING_NUMBER; i++) {
      mappings.push({
        sourceType: SourceType.Default
      });
    }
    // Construct the load-to-vector statement
    return {
      vertexName: vertex.name,
      vectorName: embeddingAttribute.name,
      mappingWidgets: [],
      mappings: mappings,
      style: {
        sourcePosition: { x: 30, y: 300 },
        targetPosition: { x: 650, y: 300 },
        middlePositions: []
      },
      dataSource: {
        Type: 'FileVar',
        Value: HelperFunctions.dataFileName
      },
      usingClauses: usingClauses,
    };
  }
}
