import { Graph } from '../topology';
import { HelperFunctions } from '../utils';
import sha256 from 'crypto-js/sha256';

import { DataSource, DataSourceType } from './data-source.interface';
import {
  DBLoadingJob<PERSON>son,
  DBLoadingStatementStyle,
} from './db-loading-job.interface';
import {
  GSQLLoadingJobJson,
  GSQLLoadToEdgeJson,
  GSQLLoadToVectorJson,
  GSQLLoadToVertexJson
} from './gsql-loading-job.interface';
import { LoadToEdgeData, LoadToEdgeLogic } from './load-to-edge.model';
import { LoadToEntityLogic } from './load-to-entity-logic.model';
import { LoadToVertexData, LoadToVertexLogic } from './load-to-vertex.model';
import { LoadToVectorData, LoadToVectorLogic } from './load-to-vector.model';
import { DataFieldSchema, DataSet, DataFormat } from './data-set.interface';
import { DataSourceLogic } from './data-source-logic.model';
import { DataSetLogic } from './data-set-logic.model';
import { SourceType } from './loading-mapping.model';

/**
 * The loading job in memory representation.
 *
 * @export
 * @interface LoadingJobData
 */
export interface LoadingJobData {
  jobName: string;
  loadingStatements: (LoadToVertexData | LoadToEdgeData | LoadToVectorData)[];
  dataSource: DataSource;
  dataSet: DataSet;
  isChanged?: boolean;
}

/**
 * The loading job name prefix for different types of data source.
 *
 * @export
 * @enum {string}
*/
export enum LoadingJobPrefix {
  FILE = 'file_load_job_',
  S3 = 'kafka_s3_load_job_',
  GCS = 'gcs_load_job_',
  ABS = 'abs_load_job_',
  OLD_S3 = 's3_load_job_',
  SNOWFLAKE = 'snowflake_load_job_'
}

/**
 * Provides APIs to handle loading job related business logic.
 *
 * @export
 * @class LoadingJobLogic
 */
export class LoadingJobLogic {
  /**
   * Clone loading job data.
   *
   * @static
   * @param {LoadingJobData} data
   * @returns {LoadingJobData}
   * @memberof LoadingJob
   */
  static clone(data: LoadingJobData): LoadingJobData {
    const result: LoadingJobData = JSON.parse(JSON.stringify(data));
    return result;
  }

  /**
   * Load information from GSQL loading job json and database json into loading job data.
   * Should do validation check before invoking this function.
   *
   * @static
   * @param {GSQLLoadingJobJson} loadingJobJson
   * @param {DBLoadingJobJson} dbLoadingJobJson
   * @returns {LoadingJobData}
   * @memberof LoadingJobLogic
   */
  static loadFromGSQLAndDB(
    gsqlLoadingJobJson: GSQLLoadingJobJson,
    dbLoadingJobJson: DBLoadingJobJson,
  ): LoadingJobData {
    // Load information from the DB loading job to the in-memory loading job.
    const dataSource = DataSourceLogic.createDataSource(
      dbLoadingJobJson.dataSourceJson.dataSourceName,
      DataSourceLogic.getDataSourceType(
        dbLoadingJobJson.dataSourceJson.type
      ),
    );

    let dataFormat: DataFormat;
    let dataSchema: DataFieldSchema[];
    let sampleData: string[][];
    if (dbLoadingJobJson.header &&
      dbLoadingJobJson.header.length > 0 &&
      dbLoadingJobJson.sampleData &&
      dbLoadingJobJson.sampleData.length > 0
    ) {
      dataFormat = dbLoadingJobJson.dataSetJson?.dataFormat || DataFormat.CSV;
      dataSchema = DataSetLogic.createTabularDataSchema(
        dbLoadingJobJson.header
      );

      dbLoadingJobJson.dataSetJson = {
        dataFormat: dbLoadingJobJson.dataSetJson?.dataFormat || DataFormat.CSV,
        dataSchema: [],
      };

      // TODO: Deprecate sample data in the future due to user data privacy concern.
      sampleData = dbLoadingJobJson.sampleData;
    } else if (dbLoadingJobJson.dataSetJson) {
      dataFormat = dbLoadingJobJson.dataSetJson.dataFormat;
      dataSchema = dbLoadingJobJson.dataSetJson.dataSchema;
      delete dbLoadingJobJson['header'];
      delete dbLoadingJobJson['sampleData'];
    }

    const dataSet: DataSet = {
      uri: dbLoadingJobJson.dataSourceJson.uri,
      parsingOptions: dbLoadingJobJson.dataSourceJson.options,
      dataFormat: dataFormat,
      dataSchema: dataSchema,
      position: dbLoadingJobJson.dataSourceJson.position,
    };

    // TODO: Deprecate sample data in the future due to user data privacy concern.
    if (sampleData) {
      dataSet['sampleData'] = sampleData;
    }

    const loadingJobData = LoadingJobLogic.createLoadingJob(
      dbLoadingJobJson.loadingJobName,
      dataSource,
      dataSet,
      [],
    );

    // Load information from the GSQL loading job to the in-memory loading job.
    // If no loading statement in the loading job, then it cannot be saved at GSQL side,
    // in this case all information are loaded from database side
    if (gsqlLoadingJobJson !== undefined) {
      // Load each loading statement
      gsqlLoadingJobJson.LoadingStatements.forEach((loadingStatement, i) => {
        if (dataFormat === DataFormat.JSON) {
          delete loadingStatement.UsingClauses['HEADER'];
          delete loadingStatement.UsingClauses['SEPARATOR'];
        }

        switch (loadingStatement.Type) {
          case 'Vertex': {
            loadingJobData.loadingStatements.push(
              LoadToVertexLogic.loadFromGSQLAndDB(
                <GSQLLoadToVertexJson>loadingStatement,
                dbLoadingJobJson.loadingStatementsStyle[i],
                dataSchema
              )
            );
            break;
          }
          case 'Edge': {
            loadingJobData.loadingStatements.push(
              LoadToEdgeLogic.loadFromGSQLAndDB(
                <GSQLLoadToEdgeJson>loadingStatement,
                dbLoadingJobJson.loadingStatementsStyle[i],
                dbLoadingJobJson.dataSetJson.dataSchema
              )
            );
            break;
          }
          case 'Vector': {
            loadingJobData.loadingStatements.push(
              LoadToVectorLogic.loadFromGSQLAndDB(
                <GSQLLoadToVectorJson>loadingStatement,
                dbLoadingJobJson.loadingStatementsStyle[i],
                dbLoadingJobJson.dataSetJson.dataSchema
              )
            );
            break;
          }
        }
      });
    }

    // Parsing succeed.
    return loadingJobData;
  }

  /**
   * Dump in-memory loading job object into GSQL loading job json and database json.
   * Should not call this function if not passing semantic check.
   * If there is no loading statements, the gsqlLoadingJobJson will be undefined.
   * Thus it won't be saved to GSQL side.
   *
   * @static
   * @param {LoadingJobData} loadingJobData
   * @param {Graph} schema
   * @returns {[GSQLLoadingJobJson, DBLoadingJobJson]}
   * @memberof LoadingJobLogic
   */
  static dumpToGSQLAndDB(
    loadingJobData: LoadingJobData,
    schema: Graph
  ): [GSQLLoadingJobJson, DBLoadingJobJson] {
    // Dump information from an in-memory loading job to a DB loading job.
    const dbLoadingJobJson: DBLoadingJobJson = {
      loadingJobName: loadingJobData.jobName,
      loadingStatementsStyle: [],
      dataSourceJson: {
        type: loadingJobData.dataSource.type,
        dataSourceName: loadingJobData.dataSource.name,
        uri: loadingJobData.dataSet.uri,
        options: loadingJobData.dataSet.parsingOptions,
        position: loadingJobData.dataSet.position,
      },
      dataSetJson: {
        dataFormat: loadingJobData.dataSet.dataFormat,
        dataSchema: [],
      }
    };

    switch (loadingJobData.dataSet.dataFormat) {
      case DataFormat.CSV:
      case DataFormat.TSV:
        // TODO: Deprecate them and use data schema in the future.
        dbLoadingJobJson['header'] = loadingJobData.dataSet.dataSchema.map(
          dataSetSchema => dataSetSchema.name
        );
        dbLoadingJobJson['sampleData'] = loadingJobData.dataSet.sampleData;
        break;
      case DataFormat.JSON:
        dbLoadingJobJson.dataSetJson.dataSchema = loadingJobData.dataSet.dataSchema;
        break;
      default:
        // TODO: Support more data formats in the future.
        break;
    }

    // Initialize a GSQL loading job.
    let gsqlLoadingJobJson: GSQLLoadingJobJson = {
      FileNames: { MyDataSource: '' },
      Type: 'Offline',
      JobName: loadingJobData.jobName,
      GraphName: schema.name,
      Headers: {},
      Filters: [],
      LoadingStatements: []
    };

    // Collect information from each loading statement
    // [GST-204] In the GSQL 'export job * as json' command, the loading statements always exported
    // following the 'temp table', 'delete vertex', 'delete edge', 'load to vertex', 'load to edge', 'load to vector'
    // order, so here before save we also need to adjust the order following the above order.

    // Collect load-to-vertex statements
    loadingJobData.loadingStatements.forEach((loadingStatement) => {
      // Collect information based on either it's load-to-vertex or load-to-edge
      if ('vertexName' in loadingStatement && !('vectorName' in loadingStatement)) {
        let gsqlLoadToEntityJson: GSQLLoadToVertexJson | GSQLLoadToEdgeJson;
        let dbLoadingStatementStyle: DBLoadingStatementStyle;
        [gsqlLoadToEntityJson, dbLoadingStatementStyle] =
          LoadToVertexLogic.dumpToGSQLAndDB(
            <LoadToVertexData>loadingStatement,
            loadingJobData.dataSet.dataFormat,
            loadingJobData.dataSet.dataSchema,
          );
        gsqlLoadingJobJson.LoadingStatements.push(gsqlLoadToEntityJson);
        dbLoadingJobJson.loadingStatementsStyle.push(dbLoadingStatementStyle);
      }
    });

    // Collect load-to-edge statements
    loadingJobData.loadingStatements.forEach((loadingStatement) => {
      // Collect information based on either it's load-to-vertex or load-to-edge
      if ('edgeName' in loadingStatement) {
        let gsqlLoadToEntityJson: GSQLLoadToVertexJson | GSQLLoadToEdgeJson;
        let dbLoadingStatementStyle: DBLoadingStatementStyle;
        [gsqlLoadToEntityJson, dbLoadingStatementStyle] =
          LoadToEdgeLogic.dumpToGSQLAndDB(
            <LoadToEdgeData>loadingStatement,
            loadingJobData.dataSet.dataFormat,
            loadingJobData.dataSet.dataSchema,
          );
        gsqlLoadingJobJson.LoadingStatements.push(gsqlLoadToEntityJson);
        dbLoadingJobJson.loadingStatementsStyle.push(dbLoadingStatementStyle);
      }
    });

    // Collect load-to-vector statements
    loadingJobData.loadingStatements.forEach((loadingStatement) => {
      if (
        'vectorName' in loadingStatement &&
        loadingStatement.mappings.length === 2 &&
        loadingStatement.mappings[0].sourceType !== SourceType.Default &&
        loadingStatement.mappings[1].sourceType === SourceType.DataSourceColumn
      ) {
        let gsqlLoadToEntityJson: GSQLLoadToVectorJson;
        let dbLoadingStatementStyle: DBLoadingStatementStyle;
        [gsqlLoadToEntityJson, dbLoadingStatementStyle] =
          LoadToVectorLogic.dumpToGSQLAndDB(
            <LoadToVectorData>loadingStatement,
            loadingJobData.dataSet.dataFormat,
            loadingJobData.dataSet.dataSchema,
          );
        gsqlLoadingJobJson.LoadingStatements.push(gsqlLoadToEntityJson);
        dbLoadingJobJson.loadingStatementsStyle.push(dbLoadingStatementStyle);
      }
    });


    // If no loading statements, make GSQL loading statements undefined.
    if (gsqlLoadingJobJson.LoadingStatements.length === 0) {
      gsqlLoadingJobJson = undefined;
    }

    return [gsqlLoadingJobJson, dbLoadingJobJson];
  }

  /**
   * Check if the GSQL loading job json is valid json, i.e., not containing unsupported features.
   *
   * @static
   * @param {GSQLLoadingJobJson} loadingJobJson
   * @param {DBLoadingJobJson} dbLoadingJobJson
   * @returns {{
   *     success: boolean,
   *     message?: string
   *   }}
   * @memberof LoadingJobLogic
   */
  static validationCheck(loadingJobJson: GSQLLoadingJobJson, dbLoadingJobJson: DBLoadingJobJson): {
    success: boolean,
    message?: string
  } {
    // 1. Offline loading job is not supported.
    if (loadingJobJson.Type === 'Online') {
      return {
        success: false,
        message: 'Online post job is deprecated in Graph Studio.'
      };
    }

    // 2. User defined inline filter is not supported.
    if (loadingJobJson.Filters.length > 0) {
      return {
        success: false,
        message: 'User defined inline filters is not supported in Graph Studio.'
      };
    }

    // 3. User defined header is not supported. Need use SrcColIndex directly
    if (Object.keys(loadingJobJson.Headers).length > 0) {
      return {
        success: false,
        message: 'User defined header is not supported in Graph Studio.'
      };
    }

    // 4. Validation check each loading statement.
    let i = 0;
    for (const loadingStatement of loadingJobJson.LoadingStatements) {
      // Do validation check based on each loading statement type.
      switch (loadingStatement.Type) {
        // Load to temp table, delete vertex | edge is not supported for now.
        case 'TempTable': {
          return {
            success: false,
            message: 'Load to temp table is not supported in Graph Studio.'
          };
        }
        case 'DeleteVertex': {
          return {
            success: false,
            message: 'Delete vertex is not supported in Graph Studio.'
          };
        }
        case 'DeleteEdge': {
          return {
            success: false,
            message: 'Delete edge is not supported in Graph Studio.'
          };
        }
        // Recursively validation check load to vertex
        case 'Vertex': {
          const validationCheckLoadToVertex =
            LoadToEntityLogic.validationCheck(<GSQLLoadToVertexJson>loadingStatement);
          if (!validationCheckLoadToVertex.success) {
            return validationCheckLoadToVertex;
          }
          break;
        }
        // Recursively validation check load to edge
        case 'Edge': {
          const validationCheckLoadToEdge =
            LoadToEntityLogic.validationCheck(<GSQLLoadToEdgeJson>loadingStatement);
          if (!validationCheckLoadToEdge.success) {
            return validationCheckLoadToEdge;
          }
          break;
        }
        case 'Vector': {
          const validationCheckLoadToVector =
            LoadToEntityLogic.validationCheck(<GSQLLoadToVectorJson>loadingStatement);
          if (!validationCheckLoadToVector.success) {
            return validationCheckLoadToVector;
          }
          break;
        }
        default: {
          return {
            success: false,
            message: `"${loadingStatement.Type}" is invalid loading statement type.`
          };
        }
      }
      i++;
    }

    // 5. GSQL loading statement number and DB loading statement number don't match
    if (loadingJobJson.LoadingStatements.length !== dbLoadingJobJson.loadingStatementsStyle.length) {
      return {
        success: false,
        message: 'Loading statement number saved in engine and backend side don\'t match.'
      };
    }

    return {
      success: true
    };
  }

  /**
   * Check if the in-memory loading job statements have any semantic errors.
   * The error message are collected in the message string array.
   * If one loading statement doesn't have any errors, the message will be empty.
   * NOTE: Must invoke this semantic check after converting job GSQL representation to in-memory
   *       loading jobs and before convert the in-memory loading jobs back to GSQL representation.
   *
   * @static
   * @param {LoadingJobData} loadingJob
   * @param {Graph} schema
   * @returns {{
   *     success: boolean,
   *     message: string[]
   *   }}
   * @memberof LoadingJobLogic
   */
  static semanticCheck(loadingJob: LoadingJobData, schema: Graph): {
    success: boolean,
    message: string[]
  } {

    // Collect semantic check result of each loading statements
    const result = {
      success: true,
      message: []
    };

    // Collect semantic check errors of each loading statement.
    loadingJob.loadingStatements.forEach((loadingStatement) => {
      const dataSchema = loadingJob.dataSet.dataSchema;
      let loadingStmtSemanticCheck: {
        success: boolean,
        message: string,
      };
      if ('vectorName' in loadingStatement) {
        // Semantic check as LoadToVector
        loadingStmtSemanticCheck = LoadToVectorLogic.semanticCheck(
          <LoadToVectorData>loadingStatement,
          dataSchema.map(dataField => dataField.name),
          schema,
        );
      } else if ('vertexName' in loadingStatement) {
        // Semantic check as LoadToVertex
        loadingStmtSemanticCheck = LoadToVertexLogic.semanticCheck(
          <LoadToVertexData>loadingStatement,
          dataSchema.map(dataField => dataField.name),
          schema,
        );
      } else {
        // Semantic check as LoadToEdge
        loadingStmtSemanticCheck = LoadToEdgeLogic.semanticCheck(
          <LoadToEdgeData>loadingStatement,
          dataSchema.map(dataField => dataField.name),
          schema,
        );
      }
      result.success = result.success && loadingStmtSemanticCheck.success;
      result.message.push(loadingStmtSemanticCheck.message);
    });

    return result;
  }

  /**
   * Create one empty loading job given data source information.
   *
   * @static
   * @param {string} jobName
   * @param {DataSource} dataSource
   * @param {DataSet} dataSet
   * @param {(LoadToVertexData | LoadToEdgeData | LoadToVectorData)[]} loadingStatements
   * @returns {LoadingJobData}
   * @memberof LoadingJobLogic
   */
  static createLoadingJob(
    jobName: string,
    dataSource: DataSource,
    dataSet: DataSet,
    loadingStatements: (LoadToVertexData | LoadToEdgeData | LoadToVectorData)[],
  ): LoadingJobData {
    return {
      jobName: jobName,
      dataSource: dataSource,
      dataSet: dataSet,
      loadingStatements: loadingStatements,
    };
  }

  /**
   * Get loading job name.
   *
   * @param {DataSourceType} dataSourceType
   * @param {string} uri
   * @returns {string}
   */
  static createLoadingJobName(
    dataSourceType: DataSourceType,
    uri: string,
  ): string {
    let jobNamePrefix = '';
    switch (dataSourceType) {
      case DataSourceType.File:
        jobNamePrefix = LoadingJobPrefix.FILE;
        break;
      case DataSourceType.S3:
        jobNamePrefix = LoadingJobPrefix.S3;
        break;
      case DataSourceType.GoogleCloudStorage:
        jobNamePrefix = LoadingJobPrefix.GCS;
        break;
      case DataSourceType.AzureBlobStorage:
        jobNamePrefix = LoadingJobPrefix.ABS;
        break;
      case DataSourceType.Snowflake:
        jobNamePrefix = LoadingJobPrefix.SNOWFLAKE;
        break;
      default:
        // TODO: Support more data source types.
        break;
    }

    return jobNamePrefix
      + sha256(
        HelperFunctions.fileNameToId(uri) +
        '_' + new Date().getTime()
      ).toString().slice(0, 10);
  }

  /**
   * Delete the loading statement with the given id.
   *
   * @static
   * @param {LoadingJobData} loadingJobData
   * @param {number} statementIndex
   * @memberof LoadingJobLogic
   */
  static removeLoadingStatement(
    loadingJobData: LoadingJobData,
    statementIndex: number
  ) {
    loadingJobData.loadingStatements.splice(statementIndex, 1);
  }
}
