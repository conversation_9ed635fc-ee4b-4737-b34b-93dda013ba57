import { useState, useRef, useCallback, useEffect } from 'react';
import { Expandable, ListItem, ListItemLabel } from '@/components/Expandable';
import { useGraphExpanded } from '@/contexts/graphExpandedContext';
// import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { PLACEMENT } from 'baseui/popover';
import { GsqlQueryMeta, QuerySyntax } from '@tigergraph/tools-models';
import { getQuerySyntax } from '@/utils/query';
import { CypherInstalled, CypherUninstalled, GSQLInstalled, GSQLUninstalled } from '@/pages/editor/graphs/icons';

import Popover from '@/pages/editor/Popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { QueriesIcon } from '@/pages/home/<USER>';
import QueryPopoverContent from '@/pages/editor/graphs/QueryPopoverContent';
import { CreateTempFileFn } from '@/pages/editor/file/hooks';
import { MdAdd, MdUpload } from 'react-icons/md';
import { Button } from '@tigergraph/app-ui-lib/button';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { InstallAll, InstallAllRef } from '@/pages/editor/query/InstallAll';
import { expand } from 'inline-style-expand-shorthand';
import { WorkspaceT } from '@/pages/workgroup/type';
import { useQueries } from '@/utils/useQueries';
import { LoadingIndicator } from '@/components/loading-indicator';
import { TetherPlacement } from 'baseui/layer';
import RunQueryDrawer from '@/pages/editor/query/RunQueryDrawer';

export interface QueryListProps {
  wp: WorkspaceT;
  graphName: string;
  expanded: boolean;
  searchText?: string;
  createTempFile: CreateTempFileFn;
}

// Individual query list item component with boundary-aware popover
function QueryListItem({
  query,
  graphName,
  createTempFile,
  onRunQuery,
}: {
  query: GsqlQueryMeta;
  graphName: string;
  createTempFile: CreateTempFileFn;
  onRunQuery: (query: GsqlQueryMeta) => void;
}) {
  const id = `${graphName}-${query.name}-item`;
  const [placement, setPlacement] = useState<TetherPlacement>(PLACEMENT.right);

  const updatePlacement = useCallback(() => {
    const element = document.getElementById(id);
    if (!element) return;

    const rect = element.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Approximate popover dimensions
    const popoverWidth = 650; // 200px table + 400px code + padding
    const popoverHeight = 500; // Approximate height

    const spaceBottom = viewportHeight - rect.bottom;
    const spaceTop = rect.top;

    // Determine best placement
    let placement: TetherPlacement = PLACEMENT.right;
    if (spaceBottom >= popoverHeight) {
      placement = PLACEMENT.rightTop;
    } else if (spaceTop >= popoverHeight) {
      placement = PLACEMENT.rightBottom;
    }
    setPlacement(placement);
  }, [id]);
  const [isOpen, setIsOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  const toPopoverPlacement = (placement: TetherPlacement) => {
    if (placement === PLACEMENT.rightTop) {
      return 'right-start';
    } else if (placement === PLACEMENT.rightBottom) {
      return 'right-end';
    } else {
      return 'right';
    }
  };

  const handleOpen = useCallback(() => {
    // Update placement first, then open after a frame to ensure DOM is ready
    updatePlacement();
    setIsOpen(true);
  }, [updatePlacement]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  useEffect(() => {
    if (isOpen) {
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as Node;
        const isOutsideContent = !contentRef.current || !contentRef.current.contains(target);

        // Check if the click is inside any popover (including nested ones)
        // BaseUI popovers use data-baseweb="popover" attribute
        const isInsidePopover =
          target instanceof Element &&
          (target.closest('[data-baseweb="popover"]') ||
            target.closest('[data-baseweb="modal"]') ||
            target.closest('[role="dialog"]') ||
            target.closest('[role="tooltip"]') ||
            // Check for dropdown menus which might also be nested
            target.closest('[data-radix-popper-content-wrapper]') ||
            target.closest('[data-state="open"]'));

        if (isOutsideContent && !isInsidePopover) {
          handleClose();
        }
      };

      // Use capture phase to ensure we get the event before other handlers
      document.addEventListener('mousedown', handleClickOutside, true);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside, true);
      };
    }
  }, [isOpen, handleClose]);

  const handleRunQuery = useCallback(() => {
    onRunQuery(query);
    handleClose();
  }, [handleClose, onRunQuery, query]);

  return (
    <Popover
      isOpen={isOpen}
      content={
        <div ref={contentRef}>
          <QueryPopoverContent
            query={query}
            graphName={graphName}
            createTempFile={createTempFile}
            onClose={handleClose}
            onRunQuery={handleRunQuery}
          />
        </div>
      }
      placement={placement}
      // we need to set popperOptions otherwise placement won't work
      popperOptions={{ placement: toPopoverPlacement(placement) }}
      ignoreBoundary={true}
      animateOutTime={200}
      onEsc={handleClose}
    >
      <div id={id}>
        <ListItem onClick={handleOpen}>
          <ListItemLabel icon={<QueryIcon query={query} />} label={query.name} />
        </ListItem>
      </div>
    </Popover>
  );
}

export default function QueryList({ wp, graphName, expanded, searchText = '', createTempFile }: QueryListProps) {
  const { getQueryListExpanded, setQueryListExpanded } = useGraphExpanded();
  const localExpanded = getQueryListExpanded(graphName);
  const [css, theme] = useStyletron();
  const installAllRef = useRef<InstallAllRef>(null);

  // RunQueryDrawer state
  const [showRunDrawer, setShowRunDrawer] = useState(false);
  const [selectedQuery, setSelectedQuery] = useState<GsqlQueryMeta | null>(null);

  // Fetch queries when expanded
  const { data: queries = [], isLoading } = useQueries(wp, graphName, localExpanded);

  // Filter queries based on search text
  const filteredQueries = queries.filter((query) => query.name.toLowerCase().includes(searchText.toLowerCase()));
  const uninstalledQueries = filteredQueries.filter((query) => !query.installed);

  // Handle opening RunQueryDrawer
  const handleRunQuery = useCallback((query: GsqlQueryMeta) => {
    setSelectedQuery(query);
    setShowRunDrawer(true);
  }, []);

  const createQuery = (syntax: QuerySyntax) => {
    const queryName = 'new_query';
    const newGSQLQueryContent =
      `CREATE OR REPLACE DISTRIBUTED QUERY ${queryName}(/* Parameters here */) FOR GRAPH ${graphName} { \n` +
      `  /* Write query logic here */ \n  PRINT "${queryName} works!"; \n}`;
    const newOpenCypherQueryContent =
      `CREATE OR REPLACE DISTRIBUTED OPENCYPHER QUERY ${queryName}(/* Parameters here */) FOR GRAPH ${graphName} { \n` +
      `  /* Write query logic here */ \n  RETURN "${queryName} works!" \n}`;

    createTempFile(true, queryName, syntax === 'GSQL' ? newGSQLQueryContent : newOpenCypherQueryContent, graphName);
  };

  const headerContent = (
    <div className="flex items-center justify-between grow">
      <ListItemLabel icon={<QueriesIcon />} label={'Queries'} />
      <div className="flex items-center gap-[4px]">
        <DropdownMenu>
          <>
            <DropdownMenuTrigger asChild>
              <Button
                kind="text"
                size="compact"
                shape="square"
                overrides={{ BaseButton: { style: { ...expand({ padding: 0 }) } } }}
              >
                <MdAdd size={20} title="New Query" color={theme.colors['icon.primary']} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onSelect={() => createQuery('GSQL')}>GSQL</DropdownMenuItem>
              <DropdownMenuItem onSelect={() => createQuery('CYPHER')}>openCypher</DropdownMenuItem>
            </DropdownMenuContent>
          </>
        </DropdownMenu>

        <Button
          onClick={(e) => {
            installAllRef.current?.installAll(uninstalledQueries);
            e.stopPropagation();
          }}
          kind="text"
          size="compact"
          shape="square"
          disabled={uninstalledQueries.length === 0}
          overrides={{ BaseButton: { style: { ...expand({ padding: 0 }) } } }}
        >
          <MdUpload
            size={20}
            title="Install All"
            color={uninstalledQueries.length > 0 ? theme.colors['icon.primary'] : theme.colors['icon.disabled']}
          />
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <Expandable
        label={headerContent}
        expanded={localExpanded}
        onExpandChange={(newExpanded) => setQueryListExpanded(graphName, newExpanded)}
      >
        {isLoading ? (
          <LoadingIndicator />
        ) : (
          filteredQueries.map((query) => (
            <QueryListItem
              key={query.name}
              query={query}
              graphName={graphName}
              createTempFile={createTempFile}
              onRunQuery={handleRunQuery}
            />
          ))
        )}
      </Expandable>
      <InstallAll ref={installAllRef} graphName={graphName} queries={uninstalledQueries} />
      {selectedQuery && (
        <RunQueryDrawer
          isOpen={showRunDrawer}
          onClose={() => setShowRunDrawer(false)}
          query={selectedQuery}
          graphName={graphName}
        />
      )}
    </>
  );
}

function QueryIcon({ query }: { query: GsqlQueryMeta }) {
  switch (true) {
    case query.installed && getQuerySyntax(query) === 'GSQL':
      return <GSQLInstalled />;
    case query.installed && getQuerySyntax(query) === 'CYPHER':
      return <CypherInstalled />;
    case !query.installed && getQuerySyntax(query) === 'GSQL':
      return <GSQLUninstalled />;
    case !query.installed && getQuerySyntax(query) === 'CYPHER':
      return <CypherUninstalled />;
  }

  return null;
}
