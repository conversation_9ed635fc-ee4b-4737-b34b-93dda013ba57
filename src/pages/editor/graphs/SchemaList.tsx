import React, { useMemo } from 'react';
import { Expandable, ListItem, ListItemLabel } from '@/components/Expandable';
import { ExternalLink, ExternalNode } from '@tigergraph/tools-models';
import { convertSchemaToGraph } from '@tigergraph/tools-ui/esm/graph/data';
import { DatabaseIcon, EdgeIcon, ExploreIcon2, VertexIcon } from '@/pages/home/<USER>';
import { WorkspaceT } from '@/pages/workgroup/type';
import { emptySchema, useSchema } from '@/utils/useSchema';
import { LoadingIndicator } from '@/components/loading-indicator';
import { useGraphExpanded } from '@/contexts/graphExpandedContext';
import { Button } from '@tigergraph/app-ui-lib/button';
import { useNavigate } from 'react-router-dom';
import { expand } from 'inline-style-expand-shorthand';
import { StatefulTipsPopover } from '@/components/tipsPopover';

export interface SchemaListProps {
  wp: WorkspaceT;
  graphName: string;
  expanded: boolean;
  searchText?: string;
  onSchemaItemSelect: (item: ExternalNode | ExternalLink, graphName: string) => void;
}

export function SchemaList({ wp, graphName, expanded, searchText = '', onSchemaItemSelect }: SchemaListProps) {
  const navigate = useNavigate();

  const { getSchemaListExpanded, setSchemaListExpanded } = useGraphExpanded();
  const localExpanded = getSchemaListExpanded(graphName);

  // Fetch schema when expanded
  const { data: schema = emptySchema, isLoading } = useSchema(wp, graphName, localExpanded);

  const vertices = schema.VertexTypes;
  const edges = schema.EdgeTypes;
  const schemaGraph = useMemo(() => {
    return convertSchemaToGraph(schema);
  }, [schema]);

  // Filter schema items based on search text
  const filteredVertices = vertices.filter(({ Name }) => Name.toLowerCase().includes(searchText.toLowerCase()));
  const filteredEdges = edges.filter(({ Name }) => Name.toLowerCase().includes(searchText.toLowerCase()));

  const handleClickSchemaItem = (name: string) => {
    const item =
      schemaGraph.nodes.find((node) => node.type === name) || schemaGraph.links.find((link) => link.type === name);
    if (item) {
      onSchemaItemSelect(item, schema.GraphName);
    }
  };

  return (
    <Expandable
      label={<ListItemLabel icon={<DatabaseIcon />} label={'Schema'} />}
      expanded={localExpanded}
      onExpandChange={(newExpanded) => setSchemaListExpanded(graphName, newExpanded)}
    >
      {isLoading ? (
        <LoadingIndicator />
      ) : (
        <>
          {filteredVertices.map(({ Name }) => (
            <SchemaListItem
              key={Name}
              graphName={graphName}
              name={Name}
              type="vertex"
              onClick={() => handleClickSchemaItem(Name)}
            />
          ))}
          {filteredEdges.map(({ Name }) => (
            <SchemaListItem
              key={Name}
              graphName={graphName}
              name={Name}
              type="edge"
              onClick={() => handleClickSchemaItem(Name)}
            />
          ))}
        </>
      )}
    </Expandable>
  );
}

interface SchemaListItemProps {
  name: string;
  type: 'vertex' | 'edge';
  onClick: () => void;
  graphName: string;
}

function SchemaListItem({ name, type, onClick, graphName }: SchemaListItemProps) {
  const navigate = useNavigate();

  const handleExploreClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate('/explore', { state: { defaultVertexEdgeType: name, graphName } });
  };

  const icon = type === 'vertex' ? <VertexIcon /> : <EdgeIcon />;

  return (
    <ListItem key={name} onClick={onClick}>
      <ListItemLabel
        icon={icon}
        label={name}
        action={
          <StatefulTipsPopover content={'Explore'} triggerType="hover" placement="top">
            <Button
              size="compact"
              kind="text"
              shape="square"
              onClick={handleExploreClick}
              overrides={{
                BaseButton: {
                  style: {
                    ...expand({ padding: '0' }),
                  },
                },
              }}
            >
              <ExploreIcon2 />
            </Button>
          </StatefulTipsPopover>
        }
      />
    </ListItem>
  );
}
