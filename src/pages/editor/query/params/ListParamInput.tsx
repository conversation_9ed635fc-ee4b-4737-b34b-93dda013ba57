import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import DeleteIcon from '@/assets/delete.svg?react';
import { Button } from '@tigergraph/app-ui-lib/button';
import { BiPlus } from 'react-icons/bi';
import { QueryParam, QueryParamListType } from '@tigergraph/tools-models';
import VertexParamInput from '@/pages/editor/query/params/VertexParamInput';
import SimpleTypeParamInput from '@/pages/editor/query/params/SimpleTypeParamInput';
import { getSimpleTypeDefaultValue } from '@/utils/queryParam';
import { expand } from 'inline-style-expand-shorthand';

interface ListParamInputProps {
  param: QueryParam;
  value: any[];
  onChange: (value: any[]) => void;
  disabled?: boolean;
  graphName: string;
  error?: string[];
}

export default function ListParamInput({
  param,
  value,
  onChange,
  disabled = false,
  graphName,
  error = [],
}: ListParamInputProps) {
  const [css, theme] = useStyletron();
  const elementType = (param.paramType as QueryParamListType).elementType;

  const handleAddItem = () => {
    onChange([...value, getSimpleTypeDefaultValue(elementType)]);
  };

  const handleRemoveItem = (index: number) => {
    const newValue = [...value];
    newValue.splice(index, 1);
    onChange(newValue);
  };

  const handleItemChange = (index: number, newItemValue: any) => {
    const newValue = [...value];
    newValue[index] = newItemValue;
    onChange(newValue);
  };

  const renderListItemInput = (itemValue: any, index: number) => {
    if (elementType.type === 'VERTEX') {
      return (
        <VertexParamInput
          param={{ paramType: elementType } as QueryParam}
          value={itemValue}
          onChange={(newValue) => handleItemChange(index, newValue)}
          disabled={disabled}
          graphName={graphName}
          hideLabel={true}
          error={error[index]}
        />
      );
    }
    return (
      <SimpleTypeParamInput
        param={{ paramType: elementType } as QueryParam}
        value={itemValue}
        onChange={(newValue) => handleItemChange(index, newValue)}
        disabled={disabled}
        error={error[index]}
        isListItem={true}
      />
    );
  };

  return (
    <div
      className={css({
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        padding: '12px',
        backgroundColor: theme.colors['background.disabled'],
        border: `1px solid ${theme.colors['border.tertiary']}`,
      })}
    >
      {elementType.type === 'VERTEX' && value.length > 0 && (
        <div className={css({ display: 'flex', gap: '8px' })}>
          <div className={css({ flex: 1 })}>
            <div
              className={css({
                color: theme.colors['dropdown.text'],
                fontWeight: 500,
                fontSize: '14px',
              })}
            >
              Vertex ID
            </div>
          </div>
          <div className={css({ flex: 1 })}>
            <div
              className={css({
                color: theme.colors['dropdown.text'],
                fontWeight: 500,
                fontSize: '14px',
                marginLeft: '8px',
              })}
            >
              Vertex Type
            </div>
          </div>
          <div className={css({ width: '40px' })}></div>
        </div>
      )}
      {value.map((item, index) => (
        <div key={index}>
          <div key={index} className="flex gap-2 justify-between">
            {renderListItemInput(item, index)}
            <div className="self-start mt-[8px]">
              <Button
                kind="text"
                size="compact"
                shape="square"
                overrides={{ BaseButton: { style: { ...expand({ padding: '0' }) } } }}
                onClick={() => handleRemoveItem(index)}
                disabled={disabled}
              >
                <DeleteIcon />
              </Button>
            </div>
          </div>
          {error[index] && elementType.type !== 'VERTEX' && (
            <div className={css({ color: theme.colors['text.danger'], fontSize: '12px', marginTop: '4px' })}>
              {error[index]}
            </div>
          )}
        </div>
      ))}
      <div className={css({ alignSelf: 'flex-start' })}>
        <Button
          kind="text"
          size="compact"
          onClick={handleAddItem}
          disabled={disabled}
          startEnhancer={<BiPlus size={16} color={theme.colors['text.link']} />}
        >
          <span className={css({ color: theme.colors['text.link'] })}>Add Item</span>
        </Button>
      </div>
    </div>
  );
}
