import { BaseFileList } from '@/pages/editor/file/BaseFileList';
import { render, screen, fireEvent, renderHook } from '@/test-utils';
import { FileStore, FilePermission, FileType, FileSortType } from '@/utils/graphEditor/data';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { cloneDeep } from 'lodash-es';
import { vi } from 'vitest';

function getMockFile(): FileStore {
  return {
    id: '',
    name: '',
    is_folder: false,
    created_at: '',
    updated_at: '',
    permission: FilePermission.Edit,
    files: [],
    content: '',
    parent_id: '',
    type: 'UserFile' as FileType,
  };
}

const mockFiles: FileStore[] = [
  {
    ...getMockFile(),
    id: '1',
    name: 'Folder 1',
    is_folder: true,
    files: [
      { ...getMockFile(), id: '2', name: 'File 1', is_folder: false, created_at: '2024-01-01' },
      { ...getMockFile(), id: '3', name: 'File 2', is_folder: false, created_at: '2024-01-02' },
    ],
  },
  { ...getMockFile(), id: '4', name: 'File 3', is_folder: false, created_at: '2024-01-01' },
  { ...getMockFile(), id: '5', name: 'File 4', is_folder: false, created_at: '2024-01-02' },
];

const defaultProps = {
  files: cloneDeep(mockFiles),
  activeFiles: [],
  folderExpanded: { '1': true },
  currentFileId: null,
  type: 'file' as const,
  isFetching: false,
  sortType: FileSortType.DateCreated,
  searchText: '',
  onToggleExpand: vi.fn(),
  renderTreeNode: vi.fn((file) => <div>{file.name}</div>),
};

describe('BaseFileList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('render', async () => {
    render(<BaseFileList {...defaultProps} />);
    expect(await screen.findByText('Folder 1')).toBeInTheDocument();
    expect(await screen.findByText('File 1')).toBeInTheDocument();
  });

  test('displays loading state correctly', async () => {
    render(<BaseFileList {...defaultProps} isFetching={true} />);
    expect(await screen.findByTestId('loader')).toBeInTheDocument();
  });

  test('displays empty state message when no files match search', async () => {
    render(<BaseFileList {...defaultProps} files={[]} />);
    expect(await screen.findByText('No files found')).toBeInTheDocument();
  });

  // test('filters files based on search input', async () => {
  //   render(<BaseFileList {...defaultProps} />);
  //   const searchInput = await screen.findByPlaceholderText('Search...');

  //   fireEvent.change(searchInput, { target: { value: 'File 1' } });
  //   expect(await screen.findByText('File 1', { timeout: 1000 })).toBeInTheDocument();
  //   expect(await screen.findByText('File 3', { timeout: 1000 })).not.toBeInTheDocument();
  // });

  test('calls onToggleExpand when folder is clicked', async () => {
    render(<BaseFileList {...defaultProps} />);
    const folderToggle = await screen.findByText('Folder 1');
    fireEvent.click(folderToggle);

    expect(defaultProps.onToggleExpand).toHaveBeenCalledWith('1');
  });

  test('sorts files by date created', async () => {
    render(<BaseFileList {...defaultProps} />);
    const files = await screen.findAllByText(/(File|Folder)\s+\d+/);
    // folder is always first
    expect(files[0]).toHaveTextContent('Folder 1');
    expect(files[1]).toHaveTextContent('File 2');
    expect(files[2]).toHaveTextContent('File 1');
    expect(files[3]).toHaveTextContent('File 4');
    expect(files[4]).toHaveTextContent('File 3');
  });

  test('sorts files by name when sortType prop is Name', async () => {
    render(<BaseFileList {...defaultProps} sortType={FileSortType.Name} />);
    const files = await screen.findAllByText(/(File|Folder)\s+\d+/);
    expect(files[0]).toHaveTextContent('Folder 1');
    expect(files[1]).toHaveTextContent('File 1');
    expect(files[2]).toHaveTextContent('File 2');
    expect(files[3]).toHaveTextContent('File 3');
    expect(files[4]).toHaveTextContent('File 4');
  });

  test('filters files based on searchText prop', async () => {
    render(<BaseFileList {...defaultProps} searchText="File 1" />);
    expect(await screen.findByText('File 1')).toBeInTheDocument();
    expect(screen.queryByText('File 2')).not.toBeInTheDocument();
    expect(screen.queryByText('File 3')).not.toBeInTheDocument();
    expect(screen.queryByText('File 4')).not.toBeInTheDocument();
  });

  test('highlights selected file', async () => {
    const { debug } = render(<BaseFileList {...defaultProps} currentFileId="2" />);
    const selectedFile = await screen.findByText('File 1');
    const {
      result: {
        current: [_, theme],
      },
    } = renderHook(() => useStyletron());
    expect(selectedFile.closest('[role="treeitem"]')).toHaveStyle({
      backgroundColor: theme.colors['list.background.selected'],
    });
  });
});
