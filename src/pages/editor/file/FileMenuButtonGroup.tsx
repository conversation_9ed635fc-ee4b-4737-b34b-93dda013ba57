import StatefulPopover from '@/pages/editor/StatefulPopover';
import { styled, useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { FileSortType } from '@/utils/graphEditor/data';
import { CheckIcon } from '@/pages/home/<USER>';
import { But<PERSON> } from '@tigergraph/app-ui-lib/button';
import { MdAdd, MdRefresh, MdSort } from 'react-icons/md';
import { StyledButton } from '@/pages/editor/file/styleObject';

const SortOption = styled('button', ({ $theme }) => ({
  paddingRight: '8px',
  paddingLeft: '8px',
  paddingTop: '8px',
  paddingBottom: '8px',
  width: '100%',
  display: 'flex',
  justifyContent: 'space-between',
  textAlign: 'left',
  color: $theme.colors['dropdown.text'],
  ':hover': {
    backgroundColor: $theme.colors['dropdown.background.hover'],
  },
}));

export interface FileMenuButtonGroupProps {
  onCreateFile: (file: { is_folder: boolean; content?: string; parentId?: string; name?: string }) => void;
  onSort: (type: FileSortType) => void;
  onRefresh: () => void;
  sortType: FileSortType;
}

export function FileMenuButtonGroup({ onCreateFile, onSort, sortType, onRefresh }: FileMenuButtonGroupProps) {
  const [css, theme] = useStyletron();

  return (
    <div className={css({ marginLeft: '16px', display: 'flex', alignItems: 'center' })}>
      <AddFilePopover onCreateFile={onCreateFile} />
      <FileSortOptions onSort={onSort} sortType={sortType} />
      <Button size="compact" kind="text" shape="square" onClick={onRefresh}>
        <MdRefresh title="refresh" size={20} color={theme.colors['button.icon']} />
      </Button>
    </div>
  );
}

interface FileSortOptionsProps {
  onSort: (type: FileSortType) => void;
  sortType: FileSortType;
}

function FileSortOptions({ onSort, sortType }: FileSortOptionsProps) {
  const [css, theme] = useStyletron();

  return (
    <StatefulPopover
      showArrow={false}
      placement={'rightTop'}
      overrides={{
        Body: {
          style: ({ $theme }) => ({
            width: '120px',
          }),
        },
        Inner: {
          style: ({ $theme }) => ({
            paddingTop: '8px',
            paddingBottom: '8px',
            paddingLeft: '0px',
            paddingRight: '0px',
          }),
        },
      }}
      content={({ close }) => (
        <>
          <SortOption
            onClick={() => {
              onSort(FileSortType.DateCreated);
              close();
            }}
          >
            Date created
            {sortType === FileSortType.DateCreated && <CheckIcon />}
          </SortOption>
          <SortOption
            onClick={() => {
              onSort(FileSortType.LastModified);
              close();
            }}
          >
            Last modified
            {sortType === FileSortType.LastModified && <CheckIcon />}
          </SortOption>
          <SortOption
            onClick={() => {
              onSort(FileSortType.Name);
              close();
            }}
          >
            Name
            {sortType === FileSortType.Name && <CheckIcon />}
          </SortOption>
        </>
      )}
    >
      <Button size="compact" kind="text" shape="square">
        <MdSort title="sortBy" size={20} color={theme.colors['button.icon']} />
      </Button>
    </StatefulPopover>
  );
}

interface AddFilePopoverProps {
  onCreateFile: (file: { is_folder: boolean; content?: string; parentId?: string; name?: string }) => void;
}

function AddFilePopover({ onCreateFile }: AddFilePopoverProps) {
  const [css, theme] = useStyletron();

  return (
    <StatefulPopover
      showArrow={false}
      placement={'rightTop'}
      overrides={{
        Body: {
          style: ({ $theme }) => ({
            width: '102px',
          }),
        },
        Inner: {
          style: ({ $theme }) => ({
            paddingTop: '8px',
            paddingBottom: '8px',
            paddingLeft: '0px',
            paddingRight: '0px',
          }),
        },
      }}
      content={({ close }) => (
        <>
          <StyledButton
            onClick={() => {
              onCreateFile({ is_folder: true });
              close();
            }}
            $style={{ height: '32px' }}
          >
            Folder
          </StyledButton>
          <StyledButton
            onClick={() => {
              onCreateFile({ is_folder: false });
              close();
            }}
            $style={{ height: '32px' }}
          >
            GSQL File
          </StyledButton>
        </>
      )}
    >
      <Button size="compact" kind="text" shape="square" aria-label={'add-file-folder'}>
        <MdAdd title="addFile" size={20} color={theme.colors['button.icon']} />
      </Button>
    </StatefulPopover>
  );
}
