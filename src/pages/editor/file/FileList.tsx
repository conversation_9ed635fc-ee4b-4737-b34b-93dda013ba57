import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Input } from '@tigergraph/app-ui-lib/input';
import searchIcon from '@/assets/search.svg';
import { ActiveFile, FileChangeType, FileStore, FileSortType } from '@/utils/graphEditor/data';
import React, { useCallback, useState } from 'react';

import { FileItem } from '@/pages/editor/file/FileItem';
import { ShareDrawer } from '@/pages/editor/file/ShareDrawer';
import { BaseFileList } from '@/pages/editor/file/BaseFileList';
import { FileMenuButtonGroup } from './FileMenuButtonGroup';

interface FileListProps {
  files: FileStore[];
  activeFiles: ActiveFile[];
  folderExpanded: Record<string, boolean>;
  currentFileId: string | null;
  newFileId: string;
  setNewFileId: React.Dispatch<React.SetStateAction<string>>;
  isFetching: boolean;
  onChangeFile: (type: FileChangeType, payload: FileStore) => void;
  onSelectFile: (file: FileStore) => void;
  onRefresh: () => void;
  onCreateFile: (file: { is_folder: boolean; content?: string; parentId?: string; name?: string }) => void;
  onToggleExpand: (id: string) => void;
  onDeleteFile: (file: FileStore) => void;
  onMoveFile: (fromId: string, parentFileId: string | null) => void;
}

const preventDefault = (e: Event) => e.preventDefault();

export function FileList({
  files,
  activeFiles,
  folderExpanded,
  currentFileId,
  newFileId,
  setNewFileId,
  isFetching,
  onChangeFile,
  onSelectFile,
  onRefresh,
  onCreateFile,
  onToggleExpand,
  onDeleteFile,
  onMoveFile,
}: FileListProps) {
  const [css, theme] = useStyletron();

  const [showShareDrawer, setShowShareDrawer] = useState<boolean>(false);
  const [shareFile, setShareFile] = useState<FileStore | null>(null);
  const [sortType, setSortType] = useState<FileSortType>(FileSortType.DateCreated);
  const [searchText, setSearchText] = useState<string>('');

  const handleSortFile = (type: FileSortType) => {
    setSortType(type);
  };

  const handleSearchTextChange = (val: string) => {
    setSearchText(val);
  };

  const renderTreeNode = useCallback(
    (file: FileStore, folder: FileStore | null, { onFileMenuOpen }: { onFileMenuOpen: (isOpen: boolean) => void }) => {
      return (
        <FileItem
          key={file.id}
          file={file}
          parent={folder}
          allFiles={files}
          newFileId={newFileId}
          setNewFileId={setNewFileId}
          onSelectFile={onSelectFile}
          onChangeFile={onChangeFile}
          onCreateFile={onCreateFile}
          onToggleExpand={onToggleExpand}
          onOpenShareDrawer={(file) => {
            setShowShareDrawer(true);
            setShareFile(file);
          }}
          onFileMenuOpen={onFileMenuOpen}
          onDeleteFile={onDeleteFile}
          onMoveFile={onMoveFile}
        />
      );
    },
    [files, newFileId, onChangeFile, onCreateFile, onSelectFile, onToggleExpand, setNewFileId, onDeleteFile, onMoveFile]
  );

  return (
    <div
      className={css({
        height: `100%`,
        width: '100%',
        flexShrink: 0,
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'hidden',
      })}
    >
      <div
        className={css({
          padding: '8px',
          display: 'flex',
          alignItems: 'center',
        })}
      >
        <Input
          overrides={{
            Root: {
              style: {
                borderTopWidth: '1px',
                borderRightWidth: '1px',
                borderBottomWidth: '1px',
                borderLeftWidth: '1px',
              },
            },
            Input: {
              style: {
                height: '32px',
                paddingLeft: '8px',
              },
            },
          }}
          onChange={(e) => handleSearchTextChange(e.currentTarget.value)}
          onKeyDown={(e) => {
            e.stopPropagation();
          }}
          placeholder="Search..."
          startEnhancer={() => <img src={searchIcon} />}
        />
        <FileMenuButtonGroup
          sortType={sortType}
          onCreateFile={onCreateFile}
          onSort={handleSortFile}
          onRefresh={onRefresh}
        />
      </div>
      <div
        className={css({
          height: 'calc(100% - 57px)',
        })}
      >
        <BaseFileList
          files={files}
          activeFiles={activeFiles}
          folderExpanded={folderExpanded}
          currentFileId={currentFileId}
          isFetching={isFetching}
          sortType={sortType}
          searchText={searchText}
          renderTreeNode={renderTreeNode}
          onToggleExpand={onToggleExpand}
        />
      </div>
      {showShareDrawer && (
        <ShareDrawer visible={showShareDrawer} file={shareFile!} onClose={() => setShowShareDrawer(false)} />
      )}
    </div>
  );
}
