import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { TreeNode, TreeView } from 'baseui/tree-view';
import { FileStore, FileSortType, ActiveFile } from '@/utils/graphEditor/data';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import FileListLoading from './FileListLoading';
import { sortFile } from '@/utils/graphEditor';

interface BaseFileListProps {
  files: FileStore[];
  activeFiles: ActiveFile[];
  folderExpanded: Record<string, boolean>;
  currentFileId: string | null;
  isFetching?: boolean;
  sortType: FileSortType;
  searchText: string;
  onToggleExpand: (id: string) => void;
  renderTreeNode: (
    file: FileStore,
    folder: FileStore | null,
    { onFileMenuOpen }: { onFileMenuOpen: (isOpen: boolean) => void }
  ) => ReactNode;
}

const preventDefault = (e: Event) => e.preventDefault();

export function BaseFileList({
  files,
  activeFiles,
  folderExpanded,
  currentFileId,
  isFetching,
  sortType,
  searchText,
  renderTreeNode,
  onToggleExpand,
}: BaseFileListProps) {
  const [css, theme] = useStyletron();
  const fileListContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollFileList, setCanScrollFileList] = useState(true);

  const currentSelectedId = activeFiles?.find((f) => f.id === currentFileId)?.file_id || currentFileId;

  useEffect(() => {
    const ref = fileListContainerRef.current;
    if (!canScrollFileList) {
      ref?.addEventListener?.('wheel', preventDefault);
      ref?.addEventListener?.('touchmove', preventDefault);
    } else {
      ref?.removeEventListener?.('wheel', preventDefault);
      ref?.removeEventListener?.('touchmove', preventDefault);
    }
    return () => {
      ref?.removeEventListener?.('wheel', preventDefault);
      ref?.removeEventListener?.('touchmove', preventDefault);
    };
  }, [canScrollFileList]);

  const onFileMenuOpen = (isOpen: boolean) => {
    setCanScrollFileList(!isOpen);
  };

  const treeNodes = useMemo(() => {
    const genTreeNode = (file: FileStore, folder: FileStore | null): TreeNode | null => {
      const getNodeLabel = () => {
        if (file.type === 'Placeholder') {
          return <FileListLoading />;
        }

        return renderTreeNode(file, folder, { onFileMenuOpen });
      };

      const getTreeNode = (childNodes: TreeNode[]): TreeNode => {
        return {
          id: file.id,
          label: getNodeLabel,
          isExpanded: file.is_folder && folderExpanded[file.id],
          isSelected: currentFileId === file.id,
          is_folder: file.is_folder,
          children: childNodes,
        };
      };

      if (file.is_folder) {
        let childFiles = sortFile(file.files || [], sortType);
        let childNodes = childFiles.map((f) => genTreeNode(f, file)).filter((node) => node !== null) as TreeNode[];
        if (file.name.toLowerCase().includes(searchText.toLowerCase()) || childNodes.length > 0) {
          return getTreeNode(childNodes);
        }
      } else if (file.name.toLowerCase().includes(searchText.toLowerCase())) {
        return getTreeNode([]);
      }

      return null;
    };

    const sortedFiles = sortFile(files, sortType);
    return sortedFiles.map((f) => genTreeNode(f, null)).filter((node) => !!node) as TreeNode[];
  }, [files, sortType, searchText, renderTreeNode, folderExpanded, currentFileId]);

  const treeView = useMemo(
    () => (
      <TreeView
        overrides={{
          Root: {
            style: {
              flex: '1',
            },
          },
          TreeLabel: {
            style: {
              fontWeight: 'inherit',
              fontSize: '12px',
              color: 'inherit',
              ':hover': {
                backgroundColor: 'transparent',
              },
            },
          },
          TreeItem: {
            style: (props: any) => {
              const selected = currentSelectedId === (props['data-nodeid'] as string);
              const expanded = props['aria-expanded'];
              return {
                borderRadius: '4px',
                marginBottom: '4px',
                overflow: 'hidden',
                backgroundColor: selected ? theme.colors['list.background.selected'] : 'transparent',
                fontWeight: selected ? '700' : '400',
                ':hover': {
                  backgroundColor: selected
                    ? theme.colors['list.background.selected']
                    : !expanded
                    ? theme.colors['list.background.hover']
                    : 'transparent',
                },
              };
            },
          },
          ExpandIcon: {
            style: () => ({
              fontSize: '16px',
              color: theme.colors['icon.primary'],
            }),
          },
          CollapseIcon: {
            style: () => ({
              fontSize: '16px',
              color: theme.colors['icon.primary'],
            }),
          },
        }}
        onToggle={(node) => {
          onToggleExpand(`${node.id}`);
        }}
        data={treeNodes}
      />
    ),
    [treeNodes, currentSelectedId, theme.colors, onToggleExpand]
  );

  return (
    <div
      ref={fileListContainerRef}
      className={css({
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflowY: 'auto',
        overflowX: 'hidden',
        '::-webkit-scrollbar': {
          width: '5px',
          backgroundColor: '#d4dadf',
          borderRadius: '20px',
        },

        '::-webkit-scrollbar-thumb': {
          backgroundColor: '#aab5bf',
          borderRadius: '20px',
        },
      })}
    >
      {isFetching ? (
        <div
          className={css({
            paddingLeft: '16px',
          })}
        >
          <FileListLoading />
        </div>
      ) : treeNodes.length > 0 ? (
        treeView
      ) : (
        <div className={css({ paddingLeft: '16px', ...theme.typography.Label })}>No files found</div>
      )}
    </div>
  );
}
