import { showToast } from '@/components/styledToasterContainer';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { useOrgContext } from '@/contexts/orgContext';
import {
  GetFilesResponse,
  GithubFile,
  SaveFileRequest,
  SaveFileResponse,
  TutorialType,
  createFileReq,
  getFiles,
  getGithubFile,
  saveFileReq,
} from '@/pages/editor/file/api';
import {
  appendUseGraph,
  getCypherTutorialFolder,
  getGSQLTutorialFolder,
  getTempFile,
  getTutorialFile,
  gsqlTutorialFolderId,
  insertOrReplaceToQuery,
} from '@/pages/editor/file/util';
import { fileStoreChange } from '@/utils/graphEditor';
import { FileChangeType, FileStore } from '@/utils/graphEditor/data';
import { getErrorMessage } from '@/utils/utils';
import { useRequest } from 'ahooks';
import { AxiosError } from 'axios';
import { cloneDeep } from 'lodash-es';
import { useCallback, useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useSearchParams } from 'react-router-dom';

export type CreateTempFileFn = (isEditQuery: boolean, queryName: string, content: string, graphName: string) => void;

export function useAutoSaveFile({ onSuccess, onError }: { onSuccess: () => void; onError: () => void }) {
  const { run: saveFile } = useRequest(saveFileReq, {
    debounceWait: 500,
    manual: true,
    onSuccess,
    onError,
  });

  return {
    saveFile,
  };
}

export function useSaveFileMutation() {
  const { setActiveFiles } = useEditorContext();

  return useMutation<SaveFileResponse, AxiosError, SaveFileRequest>('saveFile', saveFileReq, {
    onSuccess(data, request) {
      setActiveFiles((prev) =>
        prev.map((f) =>
          f.id === request.fileId || f.file_id === request.fileId ? { ...f, sourceCode: request.content } : f
        )
      );
    },
    onError(error) {
      showToast({ kind: 'negative', message: getErrorMessage(error) });
    },
  });
}

function visitFiles(files: FileStore[], callback: (file: FileStore) => void) {
  files.forEach((f) => {
    callback(f);
    f.files && visitFiles(f.files, callback);
  });
}

export function useOrgFiles() {
  const { activeFiles, setActiveFiles, currentFileId, setCurrentFileId, onToggleExpand, folderExpanded } =
    useEditorContext();
  const { currentOrg } = useOrgContext();
  const [searchParams, setSearchParams] = useSearchParams();

  const [myFiles, setMyFiles] = useState<FileStore[]>([]);
  const [sharedFiles, setSharedFiles] = useState<FileStore[]>([]);
  const [tutorialFiles, setTutorialFiles] = useState<FileStore[]>([]);

  const { isFetching: fetchingFiles, refetch } = useQuery('queryFiles', getFiles, {
    onSuccess(data) {
      if (!data || !data.Result) {
        return [];
      }

      let { shared_files, owned_files } = data.Result!;
      visitFiles(shared_files, (f) => {
        f.type = 'UserFile';
        f.isShared = true;
      });
      visitFiles(owned_files, (f) => {
        f.type = 'UserFile';
        f.isShared = false;
      });

      setMyFiles(owned_files);
      setSharedFiles(shared_files);
      setTutorialFiles([getGSQLTutorialFolder(), getCypherTutorialFolder()]);
    },
  });

  useEffect(() => {
    const fetchTutorialFiles = async (type: TutorialType, parent: FileStore) => {
      const res = (await getGithubFile(type)) as GithubFile[];
      const excludeFiles = ['load2.gsql'];
      const tutorialFiles = res
        .filter((f) => (f.name.endsWith('.gsql') || f.name.endsWith('.cypher')) && !excludeFiles.includes(f.name))
        .map((f) => getTutorialFile(f.name, f.name, parent.id, type));

      setTutorialFiles((files) => {
        parent.files = tutorialFiles;
        return [...files];
      });
    };

    Object.keys(folderExpanded).forEach((folderId) => {
      if (folderExpanded[folderId] && folderId.includes('tutorial')) {
        let folder = tutorialFiles.find((f) => f.id === folderId);
        if (!folder) {
          const gsqlTutorialFolder = tutorialFiles.find((f) => f.id === gsqlTutorialFolderId);
          folder = gsqlTutorialFolder?.files?.find((f) => f.id === folderId);
        }
        if (folder?.files?.[0].type === 'Placeholder') {
          fetchTutorialFiles(folder!.tutorialType, folder!);
        }
      }
    });
  }, [tutorialFiles, folderExpanded]);

  const queryClient = useQueryClient();
  const updateFileStore = useCallback(
    (type: FileChangeType, file: FileStore) => {
      queryClient.setQueryData<GetFilesResponse | undefined>('queryFiles', (oldData) => {
        return fileStoreChange(oldData, type, file);
      });
    },
    [queryClient]
  );

  // sync currentFileId with searchParams
  useEffect(() => {
    if (searchParams.get('fileId') !== currentFileId) {
      if (!currentFileId) {
        setSearchParams(
          (prev) => {
            const newSearchParams = new URLSearchParams(prev);
            newSearchParams.delete('fileId');
            return newSearchParams;
          },
          {
            replace: true,
          }
        );
      } else {
        setSearchParams(
          (prev) => {
            const newSearchParams = new URLSearchParams(prev);
            newSearchParams.set('fileId', currentFileId);
            return newSearchParams;
          },
          {
            replace: true,
          }
        );
      }
    }
  }, [currentFileId, searchParams, setSearchParams]);

  // auto open file if has currentFileId and active files can't find it
  useEffect(() => {
    if (currentFileId && !activeFiles.find((file) => file.id === currentFileId)) {
      let file: FileStore | undefined;
      visitFiles([...myFiles, ...sharedFiles, ...tutorialFiles], (f) => {
        if (f.id === currentFileId) {
          file = f;
        }
      });
      if (file) {
        setActiveFiles((prev) => {
          return [cloneDeep(file!), ...prev];
        });
      }
    }
    // eslint-disable-next-line
  }, [myFiles, sharedFiles, tutorialFiles]);

  useEffect(() => {
    setActiveFiles((prev) => {
      return prev.map((file) => ({ ...file, sourceCode: undefined }));
    });
    // delete sourceCode of all active files when the editor init
    // so that the editor will reload the sourceCode from the server
    // eslint-disable-next-line
  }, []);

  const handleSelectFile = useCallback(
    (file: FileStore) => {
      const tempFile = activeFiles.find((f) => f.file_id === file.id);
      if (tempFile) {
        setCurrentFileId(tempFile.id);
        return;
      }

      setCurrentFileId(file.id);
      setActiveFiles((prev) => {
        if (prev.find((item) => item.id === file.id)) {
          return prev;
        } else {
          return [cloneDeep(file), ...prev];
        }
      });
    },
    [activeFiles, setActiveFiles, setCurrentFileId]
  );

  const updateActiveFiles = useCallback(
    (type: FileChangeType, file: FileStore) => {
      if (type === FileChangeType.CREATE) {
        if (!file.is_folder) {
          setActiveFiles((prev) => {
            return [file, ...prev];
          });
          setCurrentFileId(file.id);
        }
      }
      if (type === FileChangeType.UPDATE) {
        setActiveFiles((prev) => {
          return prev.map((item) => {
            if (item.id === file.id || item.file_id === file.id) {
              return { ...item, ...file, id: item.id };
            } else {
              return item;
            }
          });
        });
      }
      if (type === FileChangeType.DELETE) {
        const newFiles = activeFiles.filter((item) => item.id !== file.id && item.file_id !== file.id);
        const currentSelectedId = activeFiles?.find((f) => f.id === currentFileId)?.file_id || currentFileId;
        if (file.id === currentSelectedId) {
          if (activeFiles.length === 1) {
            setCurrentFileId('');
          } else {
            const index = activeFiles.findIndex((f) => f.id === file.id || f.file_id === file.id);
            if (index >= activeFiles.length - 1) {
              setCurrentFileId(newFiles[newFiles.length - 1].id);
            } else {
              setCurrentFileId(newFiles[index].id);
            }
          }
        }
        setActiveFiles(newFiles);
      }
    },
    [activeFiles, currentFileId, setActiveFiles, setCurrentFileId]
  );

  const onChangeFile = useCallback(
    (type: FileChangeType, file: FileStore) => {
      updateFileStore(type, file);
      updateActiveFiles(type, file);
    },
    [updateFileStore, updateActiveFiles]
  );

  const [newFileId, setNewFileId] = useState<string>('');
  const { mutateAsync: createFile } = useMutation('createFile', createFileReq);
  const getFileName = useCallback(
    (originName?: string, parentId?: string) => {
      let newName = originName || 'Untitled';
      let siblings = myFiles.find((f) => f.id === parentId)?.files || [];
      let fileCnt = siblings.concat(activeFiles).reduce((maxCnt, file) => {
        let pattern = new RegExp(`^${newName}_?(\\d+)?$`);
        let match = file.name.match(pattern);
        if (match) {
          let cnt = match[1] ? parseInt(match[1]) : 0;
          return Math.max(cnt, maxCnt);
        }
        return maxCnt;
      }, -1);
      return `${newName}${fileCnt >= 0 ? `_${fileCnt + 1}` : ''}`;
    },
    [myFiles, activeFiles]
  );
  // temp file is created from a query
  const createTempFile = useCallback(
    (isEditQuery: boolean, queryName: string, content: string, graphName: string) => {
      const fileName = getFileName(queryName);
      content = appendUseGraph(insertOrReplaceToQuery(content), graphName);
      updateActiveFiles(FileChangeType.CREATE, getTempFile(fileName, content, isEditQuery ? queryName : '', graphName));
    },
    [getFileName, updateActiveFiles]
  );
  const handleCreateFile = useCallback(
    async (file: { is_folder: boolean; content?: string; parentId?: string; name?: string }) => {
      let { is_folder, content, parentId, name } = file;

      name = getFileName(name);

      content = content ? insertOrReplaceToQuery(content) : '';

      await createFile(
        {
          name,
          is_folder,
          content,
          parent_id: parentId || null,
        },
        {
          onSuccess(data) {
            const newFile = data.Result!;
            updateFileStore(FileChangeType.CREATE, newFile);
            updateActiveFiles(FileChangeType.CREATE, newFile);
            setNewFileId(newFile.id);

            // expand the parent folder
            if (parentId && !folderExpanded[parentId]) {
              // @ts-ignore
              onToggleExpand(parentId);
            }
          },
        }
      );
    },
    [getFileName, createFile, updateActiveFiles, updateFileStore, onToggleExpand, folderExpanded]
  );

  const createTmpFileFromQuery = useCallback(
    (name: string, content: string, graphName: string) => {
      const activeFile = activeFiles.find((f) => f.is_temp && f.query_name === name && f.graphName === graphName);
      if (activeFile) {
        setCurrentFileId(activeFile.id);
      } else {
        createTempFile(true, name, content, graphName);
      }
    },
    [activeFiles, createTempFile, setCurrentFileId]
  );

  return {
    myFiles,
    sharedFiles,
    tutorialFiles,
    fetchingFiles,
    newFileId,
    setNewFileId,
    handleSelectFile,
    onChangeFile,
    updateFileStore,
    handleCreateFile,
    refetch,
    createTempFile,
    createTmpFileFromQuery,
  };
}
