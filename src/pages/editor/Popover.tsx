import { Popover as BasePopover } from '@tigergraph/app-ui-lib/popover';
import { mergeOverrides } from 'baseui';
import { PopoverOverrides, PopoverProps } from 'baseui/popover';
import { expand } from 'inline-style-expand-shorthand';

const popoverOverrides: PopoverOverrides = {
  Body: {
    style: ({ $theme }: { $theme: any }) => ({
      backgroundColor: $theme.colors['background.alternative'],
      color: $theme.colors['dropdown.text'],
      ...expand({
        borderRadius: '2px',
        border: `1px solid ${$theme.colors.divider}`,
      }),
    }),
  },
  Inner: {
    style: ({ $theme }: { $theme: any }) => ({
      backgroundColor: $theme.colors['background.alternative'],
      ...expand({
        padding: '12px',
      }),
    }),
  },
  Arrow: {
    style: ({ $theme }: { $theme: any }) => ({
      ...expand({
        backgroundColor: $theme.colors['background.alternative'],
        border: `1px solid ${$theme.colors.divider}`,
      })
    }),
  },
};

export default function Popover({ overrides, ...props }: PopoverProps) {
  return <BasePopover overrides={mergeOverrides(popoverOverrides as any, overrides as any)} {...props} />;
}
